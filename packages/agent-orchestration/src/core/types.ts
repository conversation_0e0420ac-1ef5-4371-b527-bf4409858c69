/**
 * Core types and interfaces for the agent orchestration system
 */

import { z } from 'zod';

// Agent Status
export enum AgentStatus {
  IDLE = 'idle',
  BUSY = 'busy',
  ERROR = 'error',
  OFFLINE = 'offline'
}

// Agent Capability Types
export enum AgentCapability {
  // Analysis capabilities
  BUDGET_ANALYSIS = 'budget_analysis',
  PERFORMANCE_ANALYSIS = 'performance_analysis',
  COMPLIANCE_CHECK = 'compliance_check',
  DATA_AGGREGATION = 'data_aggregation',
  
  // Implementation capabilities
  WORKFLOW_AUTOMATION = 'workflow_automation',
  NOTIFICATION_DISPATCH = 'notification_dispatch',
  DATA_TRANSFORMATION = 'data_transformation',
  REPORT_GENERATION = 'report_generation',
  
  // Integration capabilities
  API_INTEGRATION = 'api_integration',
  DATABASE_QUERY = 'database_query',
  FILE_PROCESSING = 'file_processing',
  REAL_TIME_SYNC = 'real_time_sync'
}

// Task Priority Levels
export enum TaskPriority {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

// Execution Patterns
export enum ExecutionPattern {
  SEQUENTIAL = 'sequential',
  PARALLEL = 'parallel',
  HIERARCHICAL = 'hierarchical',
  FEEDBACK_LOOP = 'feedback_loop',
  CONSENSUS = 'consensus'
}

// Base Task Schema
export const TaskSchema = z.object({
  id: z.string(),
  type: z.string(),
  priority: z.nativeEnum(TaskPriority),
  description: z.string(),
  requiredCapabilities: z.array(z.nativeEnum(AgentCapability)),
  context: z.record(z.any()),
  timeout: z.number().optional(),
  retryCount: z.number().default(3),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date())
});

export type Task = z.infer<typeof TaskSchema>;

// Task Result Schema
export const TaskResultSchema = z.object({
  taskId: z.string(),
  agentId: z.string(),
  status: z.enum(['success', 'failure', 'partial']),
  data: z.any(),
  errors: z.array(z.string()).optional(),
  executionTime: z.number(),
  timestamp: z.date()
});

export type TaskResult = z.infer<typeof TaskResultSchema>;

// Agent Message Schema
export const AgentMessageSchema = z.object({
  id: z.string(),
  from: z.string(),
  to: z.string().or(z.array(z.string())),
  type: z.enum(['task', 'result', 'query', 'notification', 'error']),
  payload: z.any(),
  priority: z.nativeEnum(TaskPriority),
  timestamp: z.date()
});

export type AgentMessage = z.infer<typeof AgentMessageSchema>;

// Agent Configuration
export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  capabilities: AgentCapability[];
  maxConcurrentTasks: number;
  timeout: number;
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
}

// Orchestration Plan
export interface OrchestrationPlan {
  id: string;
  task: Task;
  pattern: ExecutionPattern;
  agents: AgentAssignment[];
  dependencies: TaskDependency[];
  timeout: number;
  fallbackStrategy?: FallbackStrategy;
}

export interface AgentAssignment {
  agentId: string;
  role: 'primary' | 'supporting';
  subtasks: Task[];
  priority: number;
}

export interface TaskDependency {
  taskId: string;
  dependsOn: string[];
  type: 'hard' | 'soft';
}

export interface FallbackStrategy {
  type: 'retry' | 'reassign' | 'degrade' | 'abort';
  config: Record<string, any>;
}

// Performance Metrics
export interface AgentMetrics {
  agentId: string;
  totalTasksProcessed: number;
  successRate: number;
  averageExecutionTime: number;
  currentLoad: number;
  errors: number;
  lastUpdated: Date;
}

// Collaboration Protocol
export interface CollaborationProtocol {
  messageFormat: 'json' | 'protobuf';
  communicationPattern: 'pub-sub' | 'request-response' | 'stream';
  security: {
    encryption: boolean;
    authentication: 'token' | 'certificate';
  };
  qos: {
    guaranteedDelivery: boolean;
    messageOrdering: boolean;
    maxRetries: number;
  };
}