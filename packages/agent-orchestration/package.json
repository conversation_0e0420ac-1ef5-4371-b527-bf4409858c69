{"name": "@luminar/agent-orchestration", "version": "1.0.0", "description": "Multi-agent orchestration system for coordinating L&D platform services", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "clean": "rm -rf dist"}, "dependencies": {"@luminar/shared-core": "workspace:*", "@luminar/shared-auth": "workspace:*", "eventemitter3": "^5.0.1", "p-queue": "^7.4.1", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "typescript": "^5.3.3", "vitest": "^1.1.0"}, "engines": {"node": ">=18.0.0"}, "license": "MIT"}