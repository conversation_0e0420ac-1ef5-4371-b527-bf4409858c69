// Comprehensive exports for the shared-ui library
// =============================================================================
// Core Utilities
// =============================================================================
export { cn } from './lib/utils';
export * from './lib/prop-validation';
export * from './lib/component-utilities';

// =============================================================================
// Design System
// =============================================================================
export type {
  ColorTheme,
  GlassVariant
} from './design-system';

// =============================================================================
// Providers
// =============================================================================
export { ThemeProvider } from './providers/theme-provider';
export { AuthProvider, useAuth, withAuth } from './providers/auth-provider';
export { QueryProvider } from './lib/query/provider';
export { IntegrationProvider, useIntegrationContext } from './components/integration/providers/IntegrationProvider';

// =============================================================================
// UI Components - Actions
// =============================================================================
export {
  Button,
  LuminarButton,
  LuminarIconButton,
  LuminarFab,
  LuminarDropdown,
  LuminarCommandPalette,
  LuminarCommand,
  StatefulButton
} from './components/ui/actions';

// =============================================================================
// UI Components - Display
// =============================================================================
export {
  LoadingSpinner,
  LuminarAccordion,
  LuminarAvatar,
  LuminarBadge,
  LuminarCard,
  LuminarCarousel,
  LuminarCarouselItem,
  LuminarCounter,
  LuminarDataGrid,
  LuminarDataTable,
  LuminarMetricCard,
  LuminarProgressRing,
  LuminarSkeleton,
  LuminarStatCard,
  LuminarStatsGrid,
  LuminarStats,
  LuminarTable,
  LuminarTag,
  LuminarText,
  LuminarTimeline,
  LuminarTransferList,
  LuminarTreeView,
  ProgressBar,
  Skeleton,
  Card
} from './components/ui/display';

// =============================================================================
// UI Components - Forms
// =============================================================================
export {
  LuminarInput,
  LuminarTextarea,
  LuminarCheckbox,
  LuminarRadioGroup,
  LuminarSelect,
  LuminarSwitch,
  LuminarSlider,
  LuminarForm,
  LuminarLabel,
  LuminarAutocomplete,
  // LuminarCombobox, // Disabled due to missing component
  LuminarDatePicker,
  LuminarDateRangePicker,
  LuminarColorPicker,
  AdvancedColorPicker,
  LuminarFileUpload,
  LuminarChipInput,
  LuminarRating
} from './components/ui/forms';

// =============================================================================
// UI Components - Feedback
// =============================================================================
export {
  LuminarAlertDialog,
  LuminarAlert,
  LuminarBanner,
  LuminarDrawer,
  LuminarModal,
  LuminarPopover,
  LuminarToast,
  LuminarTooltip
} from './components/ui/feedback';

// =============================================================================
// UI Components - Email
// =============================================================================
export {
  EmailCompose,
  EmailIcon,
  EmailActions,
  EmailStatusIndicator,
  EmailList
} from './components/ui/email';

// =============================================================================
// UI Components - Integration
// =============================================================================
export { AMNAWidget } from './components/integration/widgets/AMNAWidget';

// =============================================================================
// Special Components
// =============================================================================
export { SearchBar } from './components/ui/search-bar';

// =============================================================================
// Auth Components
// =============================================================================
export { UserMenu } from './components/auth/UserMenu';
export { AuthStatusIndicator } from './components/auth/AuthStatusIndicator';

// =============================================================================
// Navigation Components
// =============================================================================
export { 
  CrossAppNavigation,
  QuickNavButtons,
  AppSwitcherWidget
} from './components/navigation/CrossAppNavigation';

// =============================================================================
// Category-based exports for organized imports
// =============================================================================
export * as Actions from './components/ui/actions';
export * as Display from './components/ui/display';
export * as Forms from './components/ui/forms';
export * as Feedback from './components/ui/feedback';
export * as Email from './components/ui/email';

// =============================================================================
// Type Exports
// =============================================================================
export * from './types';

// =============================================================================
// Component Prop Types (TASK-026)
// =============================================================================
export type { 
  // Core types
  ComponentSize,
  ComponentVariant,
  GlassIntensity,
  GlassDepth,
  AnimationPreset,
  LoadingState,
  ColorTheme,
  
  // Base component props
  BaseComponentProps,
  VariantProps,
  GlassProps,
  AnimationProps,
  InteractiveProps,
  StateProps,
  IconProps,
  ThemeProps,
  ChildrenProps,
  FormComponentProps,
  LayoutProps,
  PerformanceProps,
  
  // Composite props
  StandardComponentProps,
  StandardFormComponentProps,
  StandardLayoutProps,
  StandardFeedbackProps,
  
  // Component-specific props
  ButtonBaseProps,
  InputBaseProps,
  CardBaseProps,
  ModalBaseProps,
  
  // Utility types
  RequiredProps,
  SafeHTMLProps,
  ExtendedHTMLProps,
  RefProps,
  
  // Type guards and utilities
  sizeToPixels,
  sizeToSpacing,
  defaultComponentProps,
  defaultFormProps,
  hasVariantProps,
  hasGlassProps,
  hasAnimationProps
} from './types/component-props';

// =============================================================================
// Hooks
// =============================================================================
export {
  useAuthEnhanced,
  usePermissions,
  useAuthGuard,
  useUserManagement,
  useSession,
  useRoleNavigation,
  useAdminOperations
} from './hooks/useAuthHooks';

// =============================================================================
// Specific Component Prop Types
// =============================================================================
export type { ButtonProps } from './components/ui/actions/button';
export type { LuminarCardProps } from './components/ui/display/card';
export type { LuminarInputProps } from './components/ui/forms/input';

// =============================================================================
// API & Data Fetching
// =============================================================================
// API Client and Services
export * from './lib/api';

// React Query Hooks and Utilities
export * from './lib/query';
export * from './hooks/api/index';

// =============================================================================
// Routing & Navigation
// =============================================================================
// TanStack Router utilities and components
export * from './lib/routing';