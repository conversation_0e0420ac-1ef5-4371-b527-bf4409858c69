import { createReactConfig } from '@luminar/vite-config'
import { defineConfig } from 'vite'

export default defineConfig(({ mode }) => {
  const config = createReactConfig({
    router: true,
    routesDirectory: './src/routes',
    generatedRouteTree: './src/routeTree.gen.ts',

    // Enable bundle analysis for analyze mode
    analyze: mode === 'analyze' || mode === 'profile',

    // Performance budget configuration
    performanceBudget: {
      maxAssetSize: 500000, // 500KB per asset
      maxEntrypointSize: 800000, // 800KB for main bundle
    },

    alias: {
      '@components': './src/components',
      '@hooks': './src/hooks',
      '@utils': './src/utils',
      '@types': './src/types',
      '@stores': './src/stores',
      '@services': './src/services',
      '@mocks': './src/mocks',
    },

    define: {
      __APP_NAME__: JSON.stringify('E-Connect'),
      __API_URL__: JSON.stringify(process.env.VITE_API_URL || 'http://localhost:3000'),
      __PERFORMANCE_MONITORING__: JSON.stringify(mode !== 'development'),
    },

    preview: {
      port: 5002,
      strictPort: true,
    },
  })

  // Override server configuration to ensure correct port
  config.server = {
    ...config.server,
    port: 5002,
    strictPort: true,
    host: true,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      },
    },
  }

  return config
})
