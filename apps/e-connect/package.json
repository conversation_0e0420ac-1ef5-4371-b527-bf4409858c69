{"name": "@luminar/e-connect", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:analyze": "tsc -b && vite build --config vite.config.ts --mode analyze", "build:profile": "tsc -b && vite build --config vite.config.ts --mode profile", "lint": "eslint .", "preview": "vite preview", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "bundle-analyzer": "vite-bundle-analyzer"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@luminar/shared-auth": "workspace:*", "@luminar/shared-ui": "workspace:*", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.9", "@tanstack/router-vite-plugin": "^1.125.6", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.21", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.0", "immer": "^10.1.1", "jotai": "^2.12.5", "lodash": "^4.17.21", "postcss": "^8.5.6", "react-day-picker": "^8.10.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-hotkeys-hook": "^5.1.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "react-virtual": "^2.10.4", "recharts": "^2.12.7", "socket.io-client": "^4.7.5", "sonner": "^2.0.4", "tailwindcss": "^3.4.17", "usehooks-ts": "^3.1.1", "web-vitals": "^4.2.4", "zod": "^3.25.46"}, "devDependencies": {"@babel/plugin-transform-react-jsx-development": "^7.27.1", "@eslint/js": "^9.30.1", "@luminar/vite-config": "workspace:*", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "msw": "^2.10.3", "rollup-plugin-visualizer": "^5.12.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-bundle-analyzer": "^0.12.1"}, "msw": {"workerDirectory": ["public"]}}