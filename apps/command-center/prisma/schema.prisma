// Prisma schema for Luminar Command Center
// This file defines all database models using Prisma ORM

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["prisma", "public"]
}

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema", "fullTextSearch", "postgresqlExtensions"]
}

// ===== ENUMS =====

enum AuditEventType {
  LOGIN
  LOGOUT
  LOGIN_FAILED
  ACCOUNT_LOCKED
  DATA_ACCESS
  DATA_VIEW
  DATA_CREATE
  DATA_UPDATE
  DATA_DELETE
  SECURITY_VIOLATION
  PERMISSION_DENIED
  UNAUTHORIZED_ACCESS
  SUSPICIOUS_ACTIVITY
  SECURITY_SCAN
  CONSENT_GIVEN
  CONSENT_WITHDRAWN
  DATA_RETENTION_EXECUTED
  DATA_ANONYMIZED
  COMPLIANCE_REPORT_GENERATED
  SYSTEM_EVENT
  CONFIGURATION_CHANGE
  API_ACCESS
  HTTP_REQUEST
  FILE_ACCESS
  EXPORT
  IMPORT
  
  @@schema("prisma")
}

enum AuditSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
  
  @@schema("prisma")
}

enum AuditStatus {
  SUCCESS
  FAILURE
  PENDING
  CANCELLED
  
  @@schema("prisma")
}

enum ConversationType {
  CHAT
  COMPLETION
  AGENT
  
  @@schema("prisma")
}

enum ConversationStatus {
  ACTIVE
  ARCHIVED
  DELETED
  
  @@schema("prisma")
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
  
  @@schema("prisma")
}

enum MessageStatus {
  PENDING
  SENT
  STREAMING
  COMPLETED
  FAILED
  
  @@schema("prisma")
}

enum MemoryType {
  SHORT_TERM
  LONG_TERM
  EPISODIC
  SEMANTIC
  
  @@schema("prisma")
}

enum MemoryStatus {
  ACTIVE
  CONSOLIDATED
  EXPIRED
  ARCHIVED
  
  @@schema("prisma")
}

enum TemplateCategory {
  CUSTOM
  CHAT
  CODE_GENERATION
  SUMMARIZATION
  QUESTION_ANSWERING
  ANALYSIS
  
  @@schema("prisma")
}

enum TemplateStatus {
  ACTIVE
  INACTIVE
  DEPRECATED
  
  @@schema("prisma")
}

// ===== ANALYTICS & LOGGING MODELS =====

model Analytics {
  id          String   @id @default(uuid())
  event       String
  properties  Json
  userId      String?
  sessionId   String?
  timestamp   DateTime @default(now())
  createdAt   DateTime @default(now())

  @@index([event, timestamp])
  @@index([userId])
  @@index([sessionId])
  @@schema("prisma")
}

model SearchIndex {
  id          String   @id @default(uuid())
  entityType  String
  entityId    String
  title       String
  content     String   @db.Text
  metadata    Json?
  vector      Float[]  @default([])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([entityType, entityId])
  @@index([title])
  @@schema("prisma")
}

model AuditLog {
  id          String        @id @default(uuid())
  action      String
  entityType  String
  entityId    String?
  userId      String?
  performedBy String?
  changes     Json?
  metadata    Json?
  severity    AuditSeverity @default(LOW)
  ipAddress   String?
  userAgent   String?
  timestamp   DateTime      @default(now())
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@index([action, timestamp])
  @@index([action, createdAt])
  @@index([entityType, entityId])
  @@index([userId])
  @@index([severity])
  @@schema("prisma")
}

model ErrorLog {
  id            String   @id @default(uuid())
  error_type    String
  error_message String   @db.Text
  error_stack   String?  @db.Text
  context       Json?
  createdAt     DateTime @default(now())
  
  @@index([error_type])
  @@index([createdAt])
  @@schema("prisma")
}

// ===== CORE BUSINESS MODELS =====

model User {
  id               Int       @id @default(autoincrement())
  email            String    @unique
  password         String
  name             String
  firstName        String?
  lastName         String?
  role             String    @default("user")
  isActive         Boolean   @default(true)
  isEmailVerified  Boolean   @default(false)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  lastLoginAt      DateTime?
  refreshToken     String?   @db.Text
  
  // Relations
  departments               UserDepartment[]
  roles                     UserRole[]
  skills                    UserSkill[]
  trainings                 TrainingUser[]
  submissions               WeeklySubmission[]
  emailVerificationTokens   EmailVerificationToken[]
  passwordResetTokens       PasswordResetToken[]
  refreshTokens             RefreshToken[]
  
  @@index([email])
  @@index([role])
  @@schema("public")
}

model Department {
  id          Int               @id @default(autoincrement())
  name        String            @unique
  code        String            @unique
  description String?
  isActive    Boolean           @default(true)
  managerId   Int?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  
  // Relations
  users       UserDepartment[]
  skills      Skill[]
  trainings   Training[]
  
  @@index([code])
  @@schema("public")
}

model UserDepartment {
  id           Int        @id @default(autoincrement())
  userId       Int
  departmentId Int
  startDate    DateTime   @default(now())
  endDate      DateTime?
  isPrimary    Boolean    @default(false)
  
  // Relations
  user         User       @relation(fields: [userId], references: [id])
  department   Department @relation(fields: [departmentId], references: [id])
  
  @@unique([userId, departmentId])
  @@index([userId])
  @@index([departmentId])
  @@schema("public")
}

model Role {
  id          Int          @id @default(autoincrement())
  name        String       @unique
  description String?
  permissions Json         @default("[]")
  isActive    Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  
  // Relations
  users       UserRole[]
  
  @@index([name])
  @@schema("public")
}

model UserRole {
  id        Int       @id @default(autoincrement())
  userId    Int
  roleId    Int
  assignedAt DateTime @default(now())
  assignedBy Int?
  
  // Relations
  user      User      @relation(fields: [userId], references: [id])
  role      Role      @relation(fields: [roleId], references: [id])
  
  @@unique([userId, roleId])
  @@index([userId])
  @@index([roleId])
  @@schema("public")
}

// ===== SKILLS & COMPETENCIES =====

model Skill {
  id            Int           @id @default(autoincrement())
  name          String        @unique
  category      String
  departmentId  Int?
  description   String?
  isActive      Boolean       @default(true)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  
  // Relations
  department    Department?   @relation(fields: [departmentId], references: [id])
  userSkills    UserSkill[]
  trainings     TrainingSkill[]
  
  @@index([category])
  @@index([departmentId])
  @@schema("public")
}

model UserSkill {
  id              Int       @id @default(autoincrement())
  userId          Int
  skillId         Int
  proficiencyLevel String   // Beginner, Intermediate, Advanced, Expert
  verifiedAt      DateTime?
  verifiedBy      Int?
  expiresAt       DateTime?
  
  // Relations
  user            User      @relation(fields: [userId], references: [id])
  skill           Skill     @relation(fields: [skillId], references: [id])
  
  @@unique([userId, skillId])
  @@index([userId])
  @@index([skillId])
  @@index([proficiencyLevel])
  @@schema("public")
}

// ===== TRAINING & DEVELOPMENT =====

model Training {
  id              Int               @id @default(autoincrement())
  title           String
  description     String?           @db.Text
  category        String
  departmentId    Int?
  trainerId       String?
  duration        Int?              // Duration in hours
  format          String            // Online, Classroom, Hybrid
  maxParticipants Int?
  status          String            @default("draft") // draft, published, archived
  startDate       DateTime?
  endDate         DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  
  // Relations
  department      Department?       @relation(fields: [departmentId], references: [id])
  skills          TrainingSkill[]
  users           TrainingUser[]
  materials       TrainingMaterial[]
  
  @@index([category])
  @@index([status])
  @@index([departmentId])
  @@index([startDate, endDate])
  @@schema("public")
}

model TrainingSkill {
  id         Int      @id @default(autoincrement())
  trainingId Int
  skillId    Int
  isRequired Boolean  @default(true)
  
  // Relations
  training   Training @relation(fields: [trainingId], references: [id])
  skill      Skill    @relation(fields: [skillId], references: [id])
  
  @@unique([trainingId, skillId])
  @@index([trainingId])
  @@index([skillId])
  @@schema("public")
}

model TrainingUser {
  id              Int       @id @default(autoincrement())
  trainingId      Int
  userId          Int
  status          String    @default("enrolled") // enrolled, in_progress, completed, cancelled
  enrolledAt      DateTime  @default(now())
  startedAt       DateTime?
  completedAt     DateTime?
  score           Float?
  certificateUrl  String?
  feedback        String?   @db.Text
  
  // Relations
  training        Training  @relation(fields: [trainingId], references: [id])
  user            User      @relation(fields: [userId], references: [id])
  
  @@unique([trainingId, userId])
  @@index([trainingId])
  @@index([userId])
  @@index([status])
  @@schema("public")
}

model TrainingMaterial {
  id          Int      @id @default(autoincrement())
  trainingId  Int
  title       String
  type        String   // video, document, quiz, assignment
  url         String?
  content     String?  @db.Text
  order       Int      @default(0)
  isRequired  Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  training    Training @relation(fields: [trainingId], references: [id])
  
  @@index([trainingId])
  @@index([type])
  @@schema("public")
}

// ===== VENDOR MANAGEMENT =====

model Vendor {
  id              Int              @id @default(autoincrement())
  name            String           @unique
  code            String           @unique
  description     String?
  website         String?
  contactName     String?
  contactEmail    String?
  contactPhone    String?
  address         String?
  status          String           @default("active") // active, inactive, suspended
  rating          Float?
  notes           String?          @db.Text
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  
  // Relations
  categories      VendorCategory[]
  contracts       VendorContract[]
  
  @@index([code])
  @@index([status])
  @@schema("public")
}

model VendorCategory {
  id          Int      @id @default(autoincrement())
  vendorId    Int
  category    String   // training, assessment, content, platform
  
  // Relations
  vendor      Vendor   @relation(fields: [vendorId], references: [id])
  
  @@unique([vendorId, category])
  @@index([vendorId])
  @@index([category])
  @@schema("public")
}

model VendorContract {
  id            Int       @id @default(autoincrement())
  vendorId      Int
  contractNumber String   @unique
  startDate     DateTime
  endDate       DateTime
  value         Float?
  currency      String    @default("USD")
  status        String    @default("active") // draft, active, expired, terminated
  documentUrl   String?
  notes         String?   @db.Text
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  vendor        Vendor    @relation(fields: [vendorId], references: [id])
  
  @@index([vendorId])
  @@index([status])
  @@index([startDate, endDate])
  @@schema("public")
}

// ===== ANALYTICS & REPORTING =====

model Report {
  id          String   @id @default(uuid())
  name        String
  type        String   // training_summary, skill_gap, vendor_performance
  parameters  Json
  format      String   @default("pdf") // pdf, excel, csv
  status      String   @default("pending") // pending, processing, completed, failed
  fileUrl     String?
  createdBy   Int?
  createdAt   DateTime @default(now())
  completedAt DateTime?
  error       String?  @db.Text
  
  @@index([type])
  @@index([status])
  @@index([createdBy])
  @@index([createdAt])
  @@schema("prisma")
}

model FeatureFlag {
  id          String   @id @default(uuid())
  feature     String   @unique
  enabled     Boolean  @default(false)
  rollout     Float    @default(0) // 0-100 percentage
  conditions  Json?    // User segments, environments, etc.
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([feature])
  @@index([enabled])
  @@schema("prisma")
}

model SystemMetric {
  id          String   @id @default(uuid())
  metric      String
  value       Float
  tags        Json     @default("{}")
  timestamp   DateTime @default(now())
  
  @@index([metric, timestamp])
  @@index([timestamp])
  @@schema("prisma")
}

// ===== WEEKLY SUBMISSION SYSTEM =====

model WeeklySubmission {
  id                Int      @id @default(autoincrement())
  userId            Int
  weekStartDate     DateTime
  weekEndDate       DateTime
  accomplishments   String   @db.Text
  challenges        String?  @db.Text
  nextWeekPlans     String?  @db.Text
  additionalNotes   String?  @db.Text
  status            String   @default("draft") // draft, submitted, reviewed
  submittedAt       DateTime?
  reviewedAt        DateTime?
  reviewedBy        Int?
  reviewNotes       String?  @db.Text
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  // Relations
  user              User     @relation(fields: [userId], references: [id])
  
  @@unique([userId, weekStartDate])
  @@index([userId])
  @@index([weekStartDate])
  @@index([status])
  @@schema("public")
}

// ===== AI & AGENT CONFIGURATION =====

model FeatureConfig {
  id          String   @id @default(uuid())
  feature     String
  feature_key String
  config      Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([feature, feature_key])
  @@index([feature])
  @@schema("prisma")
}

// ===== AI CONVERSATION MODELS =====

model AiConversation {
  id              String             @id @default(uuid())
  userId          String
  title           String
  description     String?            @db.Text
  type            ConversationType   @default(CHAT)
  status          ConversationStatus @default(ACTIVE)
  model           String             @default("gpt-3.5-turbo")
  systemPromptId  String?
  modelConfig     Json?
  memoryConfig    Json?
  contextWindow   Int                @default(4096)
  tags            String[]           @default([])
  metadata        Json?
  lastMessageAt   DateTime?
  archivedAt      DateTime?
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt

  // Relations
  messages        AiMessage[]
  memories        AiMemory[]
  systemPrompt    PromptTemplate? @relation(fields: [systemPromptId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([lastMessageAt])
  @@schema("prisma")
}

model AiMessage {
  id              String        @id @default(uuid())
  conversationId  String
  role            MessageRole
  content         String        @db.Text
  attachments     Json?
  metadata        Json?
  status          MessageStatus @default(SENT)
  tokens          Int?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  conversation    AiConversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([role])
  @@index([status])
  @@index([createdAt])
  @@schema("prisma")
}

model AiMemory {
  id              String      @id @default(uuid())
  conversationId  String?
  userId          String?
  key             String
  content         String      @db.Text
  type            MemoryType  @default(SHORT_TERM)
  status          MemoryStatus @default(ACTIVE)
  importance      Float       @default(0.0)
  accessCount     Int         @default(0)
  lastAccessedAt  DateTime?
  consolidatedAt  DateTime?
  expiresAt       DateTime?
  metadata        Json?
  vector          Float[]     @default([])
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  conversation    AiConversation? @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@index([conversationId])
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([key])
  @@index([importance])
  @@schema("prisma")
}

model PromptTemplate {
  id              String           @id @default(uuid())
  name            String           @unique
  description     String?          @db.Text
  content         String           @db.Text
  variables       String[]         @default([])
  category        TemplateCategory @default(CUSTOM)
  status          TemplateStatus   @default(ACTIVE)
  version         String           @default("1.0.0")
  tags            String[]         @default([])
  usage_count     Int              @default(0)
  metadata        Json?
  createdBy       String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relations
  conversations   AiConversation[]

  @@index([category])
  @@index([status])
  @@index([name])
  @@schema("prisma")
}

// ===== DOCUMENT MANAGEMENT MODELS =====

model Document {
  id          String   @id @default(uuid())
  title       String
  content     String?  @db.Text
  type        String
  ownerId     String
  folderId    String?
  tags        String[] @default([])
  metadata    Json?
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  folder      Folder?       @relation(fields: [folderId], references: [id])
  chunks      DocumentChunk[]
  
  @@index([ownerId])
  @@index([folderId])
  @@index([type])
  @@schema("prisma")
}

model DocumentChunk {
  id         String   @id @default(uuid())
  documentId String
  content    String   @db.Text
  index      Int
  tokens     Int?
  vector     Float[]  @default([])
  metadata   Json?
  createdAt  DateTime @default(now())
  
  // Relations
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  
  @@index([documentId])
  @@index([index])
  @@schema("prisma")
}

model Folder {
  id          String     @id @default(uuid())
  name        String
  path        String
  parentId    String?
  ownerId     String
  isPublic    Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  // Relations
  parent      Folder?    @relation("FolderHierarchy", fields: [parentId], references: [id])
  children    Folder[]   @relation("FolderHierarchy")
  documents   Document[]
  
  @@index([ownerId])
  @@index([parentId])
  @@index([path])
  @@schema("prisma")
}

model Tag {
  id          String   @id @default(uuid())
  name        String   @unique
  color       String?
  description String?
  createdAt   DateTime @default(now())
  
  @@index([name])
  @@schema("prisma")
}

// ===== INTEGRATION MODELS =====

model UserIntegrationPreferences {
  id            String   @id @default(uuid())
  userId        String
  platform      String
  preferences   Json     @default("{}")
  isEnabled     Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@unique([userId, platform])
  @@index([userId])
  @@index([platform])
  @@schema("prisma")
}

model IntegrationLogs {
  id           String   @id @default(uuid())
  platform     String
  operation    String
  status       String
  details      Json?
  error        String?  @db.Text
  processingTime Int?
  createdAt    DateTime @default(now())
  
  @@index([platform])
  @@index([status])
  @@index([createdAt])
  @@schema("prisma")
}

// ===== GDPR & COMPLIANCE MODELS =====

model ConsentRecord {
  id              String   @id @default(uuid())
  userId          String
  consentType     String
  status          String   // given, withdrawn, expired
  purpose         String
  legalBasis      String?
  givenAt         DateTime
  withdrawnAt     DateTime?
  expiresAt       DateTime?
  metadata        Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@index([userId])
  @@index([consentType])
  @@index([status])
  @@schema("prisma")
}

model DataRequest {
  id          String   @id @default(uuid())
  userId      String
  type        String   // access, deletion, portability
  status      String   // pending, processing, completed, denied
  details     Json?
  response    Json?
  requestedAt DateTime @default(now())
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([userId])
  @@index([type])
  @@index([status])
  @@schema("prisma")
}

model DataProcessingLog {
  id           String   @id @default(uuid())
  operation    String
  entityType   String
  entityId     String?
  userId       String?
  purpose      String
  legalBasis   String?
  dataTypes    String[] @default([])
  details      Json?
  timestamp    DateTime @default(now())
  
  @@index([operation])
  @@index([entityType])
  @@index([userId])
  @@index([timestamp])
  @@schema("prisma")
}

model DataRetentionPolicy {
  id              String   @id @default(uuid())
  entityType      String
  retentionPeriod Int      // in days
  description     String?
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@index([entityType])
  @@index([isActive])
  @@schema("prisma")
}

model PersonalDataInventory {
  id           String   @id @default(uuid())
  dataType     String
  category     String
  description  String?
  location     String
  retention    Int?     // in days
  isProcessing Boolean  @default(true)
  legalBasis   String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@index([dataType])
  @@index([category])
  @@index([isProcessing])
  @@schema("prisma")
}

model ComplianceReport {
  id          String   @id @default(uuid())
  type        String
  status      String   @default("pending")
  parameters  Json?
  results     Json?
  generatedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([type])
  @@index([status])
  @@index([generatedAt])
  @@schema("prisma")
}

// ===== ERROR TRACKING & MONITORING MODELS =====

model ErrorGroup {
  id          String       @id @default(uuid())
  title       String
  type        String
  fingerprint String       @unique
  status      String       @default("open")
  firstSeen   DateTime     @default(now())
  lastSeen    DateTime     @default(now())
  eventCount  Int          @default(0)
  userCount   Int          @default(0)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  
  // Relations
  events      ErrorEvent[]
  resolutions ErrorResolution[]
  
  @@index([status])
  @@index([type])
  @@index([lastSeen])
  @@schema("prisma")
}

model ErrorEvent {
  id          String     @id @default(uuid())
  groupId     String
  message     String     @db.Text
  stack       String?    @db.Text
  level       String     @default("error")
  platform    String?
  release     String?
  environment String?
  userId      String?
  context     Json?
  tags        Json?
  timestamp   DateTime   @default(now())
  
  // Relations
  group       ErrorGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  
  @@index([groupId])
  @@index([level])
  @@index([timestamp])
  @@index([userId])
  @@schema("prisma")
}

model ErrorResolution {
  id          String     @id @default(uuid())
  groupId     String
  status      String     // resolved, ignored, feedback
  resolution  String?    @db.Text
  resolvedBy  String?
  resolvedAt  DateTime   @default(now())
  
  // Relations
  group       ErrorGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  
  @@index([groupId])
  @@index([status])
  @@schema("prisma")
}

model ActivityLog {
  id          String   @id @default(uuid())
  userId      String
  activity    String
  details     Json?
  ipAddress   String?
  userAgent   String?
  timestamp   DateTime @default(now())
  
  @@index([userId])
  @@index([activity])
  @@index([timestamp])
  @@schema("prisma")
}

// ===== ALERTING & MONITORING MODELS =====

model Alert {
  id          String      @id @default(uuid())
  ruleId      String
  status      String      @default("active")
  severity    String      @default("medium")
  title       String
  description String?     @db.Text
  metadata    Json?
  triggeredAt DateTime    @default(now())
  resolvedAt  DateTime?
  
  // Relations
  rule        AlertRule   @relation(fields: [ruleId], references: [id])
  
  @@index([ruleId])
  @@index([status])
  @@index([severity])
  @@index([triggeredAt])
  @@schema("prisma")
}

model AlertRule {
  id          String   @id @default(uuid())
  name        String
  description String?  @db.Text
  condition   Json
  threshold   Json?
  isEnabled   Boolean  @default(true)
  channels    String[] @default([])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  alerts      Alert[]
  
  @@index([name])
  @@index([isEnabled])
  @@schema("prisma")
}

// ===== EMAIL MANAGEMENT MODELS =====

model EmailAccount {
  id           String        @id @default(uuid())
  userId       String
  email        String
  provider     String        // gmail, outlook, etc
  accessToken  String?       @db.Text
  refreshToken String?       @db.Text
  isActive     Boolean       @default(true)
  lastSyncAt   DateTime?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  
  // Relations
  syncLogs     EmailSyncLog[]
  
  @@unique([userId, email])
  @@index([userId])
  @@index([provider])
  @@schema("prisma")
}

model EmailSyncLog {
  id            String       @id @default(uuid())
  accountId     String
  operation     String       // sync, send, delete
  status        String       // success, error, partial
  messageCount  Int?
  error         String?      @db.Text
  startedAt     DateTime     @default(now())
  completedAt   DateTime?
  
  // Relations
  account       EmailAccount @relation(fields: [accountId], references: [id], onDelete: Cascade)
  
  @@index([accountId])
  @@index([status])
  @@index([startedAt])
  @@schema("prisma")
}

// ===== FILE MANAGEMENT MODELS =====

model File {
  id          String        @id @default(uuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  path        String
  ownerId     String
  isPublic    Boolean       @default(false)
  metadata    Json?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  
  // Relations
  versions    FileVersion[]
  
  @@index([ownerId])
  @@index([filename])
  @@index([mimeType])
  @@schema("prisma")
}

model FileVersion {
  id          String   @id @default(uuid())
  fileId      String
  version     Int
  size        Int
  path        String
  checksum    String?
  createdBy   String
  createdAt   DateTime @default(now())
  
  // Relations
  file        File     @relation(fields: [fileId], references: [id], onDelete: Cascade)
  
  @@unique([fileId, version])
  @@index([fileId])
  @@index([createdBy])
  @@schema("prisma")
}

// ===== ANALYTICS & FEATURE TRACKING MODELS =====

model FeatureUsage {
  id          String   @id @default(uuid())
  userId      String?
  feature     String
  action      String
  count       Int      @default(1)
  metadata    Json?
  date        DateTime @default(now())
  
  @@unique([userId, feature, action, date])
  @@index([feature])
  @@index([action])
  @@index([date])
  @@schema("prisma")
}

model FeatureMetrics {
  id          String   @id @default(uuid())
  feature     String
  metric      String
  value       Float
  metadata    Json?
  timestamp   DateTime @default(now())
  
  @@index([feature])
  @@index([metric])
  @@index([timestamp])
  @@schema("prisma")
}

model FeatureAdoption {
  id          String   @id @default(uuid())
  userId      String
  feature     String
  adoptedAt   DateTime @default(now())
  metadata    Json?
  
  @@unique([userId, feature])
  @@index([feature])
  @@index([adoptedAt])
  @@schema("prisma")
}

model UserEvent {
  id          String   @id @default(uuid())
  userId      String
  event       String
  properties  Json?
  timestamp   DateTime @default(now())
  
  @@index([userId])
  @@index([event])
  @@index([timestamp])
  @@schema("prisma")
}

// ===== DASHBOARD & UI MODELS =====

model Dashboard {
  id          String           @id @default(uuid())
  name        String
  description String?
  layout      Json?
  isPublic    Boolean          @default(false)
  ownerId     String
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  
  // Relations
  widgets     DashboardWidget[]
  
  @@index([ownerId])
  @@index([name])
  @@schema("prisma")
}

model DashboardWidget {
  id            String    @id @default(uuid())
  dashboardId   String
  type          String
  title         String?
  configuration Json
  position      Json      // x, y, width, height
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Relations
  dashboard     Dashboard @relation(fields: [dashboardId], references: [id], onDelete: Cascade)
  
  @@index([dashboardId])
  @@index([type])
  @@schema("prisma")
}

// ===== AUTHENTICATION & SECURITY MODELS =====

model RefreshToken {
  id         String    @id @default(uuid())
  token      String    @unique
  userId     Int
  expiresAt  DateTime
  revokedAt  DateTime?
  createdAt  DateTime  @default(now())
  
  // Relations
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([token])
  @@index([expiresAt])
  @@schema("prisma")
}

model AuthAttempt {
  id          String   @id @default(uuid())
  type        String   // login, register, refresh, reset_password
  result      String   // success, failed, blocked
  userId      String?
  email       String?
  ipAddress   String?
  userAgent   String?
  details     Json?
  createdAt   DateTime @default(now())
  
  @@index([type])
  @@index([result])
  @@index([email])
  @@index([ipAddress])
  @@index([createdAt])
  @@schema("prisma")
}

model Permission {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  resource    String
  action      String
  conditions  Json?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([resource])
  @@index([action])
  @@index([name])
  @@schema("prisma")
}

model UserSession {
  id             String    @id @default(uuid())
  userId         String
  sessionId      String    @unique
  sessionToken   String    @unique
  status         String    @default("active") // active, expired, revoked
  ipAddress      String?
  userAgent      String?
  deviceInfo     Json?
  metadata       Json?
  lastActivity   DateTime  @default(now())
  lastActivityAt DateTime  @default(now())
  terminatedAt   DateTime?
  expiresAt      DateTime
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  
  @@index([userId])
  @@index([sessionId])
  @@index([sessionToken])
  @@index([status])
  @@index([expiresAt])
  @@schema("prisma")
}

// ===== NOTIFICATIONS MODELS =====

model NotificationChannel {
  id          String   @id @default(uuid())
  name        String
  type        String   // email, slack, webhook, sms
  configuration Json
  isEnabled   Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([type])
  @@index([isEnabled])
  @@schema("prisma")
}

model EmailVerificationToken {
  id         String   @id @default(uuid())
  userId     Int
  token      String   @unique
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  
  // Relations
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([token])
  @@schema("prisma")
}

model PasswordResetToken {
  id         String   @id @default(uuid())
  userId     Int
  token      String   @unique
  expiresAt  DateTime
  usedAt     DateTime?
  createdAt  DateTime @default(now())
  
  // Relations
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([token])
  @@schema("prisma")
}