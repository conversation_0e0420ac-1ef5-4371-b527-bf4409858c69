// Import necessary types for UserWithRoles interface
import { 
  User as PrismaUser, 
  Role as PrismaRole, 
  User<PERSON><PERSON> as PrismaUserRole,
  EmailVerificationToken as PrismaEmailVerificationToken,
  PasswordResetToken as PrismaPasswordResetToken,
  RefreshToken as PrismaRefreshToken,
} from '@prisma/client';

// Re-export all Prisma types
export * from '@prisma/client';

// Note: All types are already exported via export * from '@prisma/client' above
// EmailVerificationToken and PasswordResetToken are already defined in the Prisma schema

// Extended user type with relationships
export interface UserWithRoles extends PrismaUser {
  roles?: (PrismaUserRole & { role: PrismaRole })[];
}

// Additional enums not in Prisma schema but used in the codebase
export enum SessionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  SUSPENDED = 'suspended',
  TERMINATED = 'terminated',
}

// Map string values to match what's in the database
export const AuthAttemptType = {
  LOGIN: 'login',
  REGISTER: 'register',
  REFRESH: 'refresh',
  PASSWORD_RESET: 'reset_password',
} as const;

export type AuthAttemptType = typeof AuthAttemptType[keyof typeof AuthAttemptType];

export const AuthAttemptResult = {
  SUCCESS: 'success',
  FAILURE: 'failed',
  BLOCKED: 'blocked',
} as const;

export type AuthAttemptResult = typeof AuthAttemptResult[keyof typeof AuthAttemptResult];

// All Prisma enums are already available via the export * from '@prisma/client' above
// Additional type helpers for security-related enums that are used in the codebase

export const SecurityEventType = {
  POLICY_VIOLATION: 'POLICY_VIOLATION',
  INTRUSION_ATTEMPT: 'INTRUSION_ATTEMPT',
  DATA_BREACH: 'DATA_BREACH',
} as const;

export type SecurityEventType = typeof SecurityEventType[keyof typeof SecurityEventType];

export const SecurityThreatLevel = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL',
} as const;

export type SecurityThreatLevel = typeof SecurityThreatLevel[keyof typeof SecurityThreatLevel];

export const SecurityEventStatus = {
  ACTIVE: 'ACTIVE',
  RESOLVED: 'RESOLVED',
  INVESTIGATING: 'INVESTIGATING',
} as const;

export type SecurityEventStatus = typeof SecurityEventStatus[keyof typeof SecurityEventStatus];

// Audit-related enums (from Prisma schema)
export enum AuditEventType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  LOGIN_FAILED = 'LOGIN_FAILED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  DATA_ACCESS = 'DATA_ACCESS',
  DATA_VIEW = 'DATA_VIEW',
  DATA_CREATE = 'DATA_CREATE',
  DATA_UPDATE = 'DATA_UPDATE',
  DATA_DELETE = 'DATA_DELETE',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  CONSENT_GIVEN = 'CONSENT_GIVEN',
  CONSENT_WITHDRAWN = 'CONSENT_WITHDRAWN',
  DATA_RETENTION_EXECUTED = 'DATA_RETENTION_EXECUTED',
  DATA_ANONYMIZED = 'DATA_ANONYMIZED',
  COMPLIANCE_REPORT_GENERATED = 'COMPLIANCE_REPORT_GENERATED',
  SECURITY_SCAN = 'SECURITY_SCAN',
  INTEGRATION_SYNC = 'INTEGRATION_SYNC',
  INTEGRATION_ERROR = 'INTEGRATION_ERROR',
  SYSTEM_STARTUP = 'SYSTEM_STARTUP',
  SYSTEM_SHUTDOWN = 'SYSTEM_SHUTDOWN',
  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  ROLE_CREATED = 'ROLE_CREATED',
  ROLE_UPDATED = 'ROLE_UPDATED',
  ROLE_DELETED = 'ROLE_DELETED',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PERMISSION_REVOKED = 'PERMISSION_REVOKED',
  HTTP_REQUEST = 'HTTP_REQUEST',
  SYSTEM_EVENT = 'SYSTEM_EVENT',
}

export enum AuditSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export enum AuditStatus {
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
  WARNING = 'WARNING',
  INFO = 'INFO',
}
