import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../database/prisma/prisma.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createReadStream } from 'fs';
import { Readable } from 'stream';

import { File, FileVersion } from '../../common/types/prisma.types';
import { CreateFileDto } from './dto/create-file.dto';
import { UpdateFileDto } from './dto/update-file.dto';
import { ListFilesDto } from './dto/list-files.dto';
import { ProcessFileDto, ProcessingOperation } from './dto/process-file.dto';
import { StorageService } from './services/storage.service';
import { FileValidationService } from './services/file-validation.service';
import { FileProcessingService } from './services/file-processing.service';
import { VirusScanningService } from './services/virus-scanning.service';

@Injectable()
export class FilesService {
  private readonly logger = new Logger(FilesService.name);

  constructor(
    private readonly prisma: PrismaService,
    @InjectQueue('file-processing')
    private readonly fileQueue: Queue,
    private readonly configService: ConfigService,
    private readonly storageService: StorageService,
    private readonly fileValidationService: FileValidationService,
    private readonly fileProcessingService: FileProcessingService,
    private readonly virusScanningService: VirusScanningService,
  ) {}

  async uploadFile(
    file: Express.Multer.File,
    createFileDto: CreateFileDto,
  ): Promise<File> {
    try {
      // Read file buffer
      const buffer = await fs.readFile(file.path);

      // Validate file
      const validationResult = await this.fileValidationService.validateFile(
        buffer,
        file.originalname,
        file.mimetype,
      );

      if (!validationResult.isValid) {
        await this.cleanupTempFile(file.path);
        throw new BadRequestException(
          `File validation failed: ${validationResult.errors.join(', ')}`,
        );
      }

      // Store file
      const storageResult = await this.storageService.store(
        {
          buffer,
          size: file.size,
          mimeType: validationResult.detectedMimeType || file.mimetype,
          originalName: file.originalname,
        },
        {
          isPublic: createFileDto.isPublic,
          directory: validationResult.fileType,
        },
      );

      // Create file entity
      const savedFile = await this.prisma.file.create({
        data: {
          filename: path.basename(storageResult.path),
          originalName: file.originalname,
          mimeType: validationResult.detectedMimeType || file.mimetype,
          extension: path.extname(file.originalname).toLowerCase().slice(1),
          size: file.size,
          type: validationResult.fileType,
          checksum: validationResult.checksum,
          status: 'PROCESSING',
          path: storageResult.path,
          publicUrl: storageResult.url,
          tags: createFileDto.tags || [],
          isPublic: createFileDto.isPublic || false,
          isTemporary: createFileDto.isTemporary || false,
          ownerId: 1, // TODO: Get from auth context
          folderId: createFileDto.folderId,
          metadata: {},
        },
      });

      // Queue for processing
      await this.queueFileProcessing(savedFile.id);

      // Cleanup temp file
      await this.cleanupTempFile(file.path);

      return savedFile;
    } catch (error) {
      this.logger.error(`File upload failed: ${error.message}`, error.stack);
      // Cleanup on error
      if (file.path) {
        await this.cleanupTempFile(file.path);
      }
      throw error;
    }
  }

  async uploadMultipleFiles(
    files: Express.Multer.File[],
    createFileDto: CreateFileDto,
  ): Promise<File[]> {
    const uploadPromises = files.map((file) =>
      this.uploadFile(file, createFileDto),
    );
    return Promise.all(uploadPromises);
  }

  async listFiles(query: ListFilesDto) {
    const {
      page,
      limit,
      search,
      type,
      status,
      tags,
      uploadedById,
      sortBy,
      sortOrder,
    } = query;

    const where: any = {};

    if (search) {
      where.originalName = { contains: search };
    }

    if (type) {
      where.type = type;
    }

    if (status) {
      where.status = status;
    }

    if (uploadedById) {
      where.uploadedById = uploadedById;
    }

    if (tags && tags.length > 0) {
      where.tags = { hasSome: tags };
    }

    const [files, total] = await Promise.all([
      this.prisma.file.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
        include: { uploadedBy: true },
      }),
      this.prisma.file.count({ where }),
    ]);

    return {
      files,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getFile(id: string): Promise<File> {
    const file = await this.prisma.file.findUnique({
      where: { id },
      include: { uploadedBy: true, versions: true },
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    // Update access statistics
    await this.prisma.file.update({
      where: { id },
      data: {
        viewCount: { increment: 1 },
        lastAccessedAt: new Date(),
      },
    });

    return file;
  }

  async downloadFile(id: string): Promise<{ file: File; stream: Readable }> {
    const file = await this.getFile(id);

    // Update download count
    await this.prisma.file.update({
      where: { id: file.id },
      data: {
        downloadCount: { increment: 1 },
      },
    });

    const stream = (await this.storageService.retrieve(
      file.path,
    )) as Readable;

    return { file, stream };
  }

  async streamFile(id: string, res: Response, range?: string) {
    const file = await this.getFile(id);

    if (!['video', 'audio'].includes(file.type)) {
      throw new BadRequestException('File is not streamable');
    }

    const fileSize = Number(file.size);

    if (range) {
      const parts = range.replace(/bytes=/, '').split('-');
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunkSize = end - start + 1;

      res.writeHead(206, {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunkSize,
        'Content-Type': file.mimeType,
      });

      const stream = (await this.storageService.retrieve(
        file.path,
      )) as Readable;
      stream.pipe(res);
    } else {
      res.writeHead(200, {
        'Content-Length': fileSize,
        'Content-Type': file.mimeType,
      });

      const stream = (await this.storageService.retrieve(
        file.path,
      )) as Readable;
      stream.pipe(res);
    }
  }

  async getThumbnail(
    id: string,
    size: 'small' | 'medium' | 'large',
  ): Promise<string> {
    const file = await this.getFile(id);

    if (!file.variants || !file.variants[`thumbnail_${size}`]) {
      throw new NotFoundException('Thumbnail not found');
    }

    return file.variants[`thumbnail_${size}`].path;
  }

  async getVariants(id: string) {
    const file = await this.getFile(id);
    return file.variants || {};
  }

  async processFile(id: string, processFileDto: ProcessFileDto) {
    const file = await this.getFile(id);

    // Queue specific processing operation
    const job = await this.fileQueue.add('process-file', {
      fileId: file.id,
      filePath: file.path,
      operation: processFileDto.operation,
      options: processFileDto.options,
    });

    return {
      message: 'Processing started',
      jobId: job.id,
    };
  }

  async deleteFile(id: string): Promise<void> {
    const file = await this.getFile(id);

    // Delete from storage
    await this.storageService.delete(file.path);

    // Delete thumbnails and variants
    const variants = (file.metadata as any)?.variants;
    if (variants) {
      for (const variant of Object.values(variants)) {
        await this.storageService.delete((variant as any).path);
      }
    }

    // Mark as deleted (soft delete)
    await this.prisma.file.update({
      where: { id: file.id },
      data: {
        status: 'DELETED',
        deletedAt: new Date(),
      },
    });
  }

  async scanFile(id: string) {
    const file = await this.getFile(id);

    // Queue virus scanning
    const job = await this.fileQueue.add('scan-virus', {
      fileId: file.id,
      filePath: file.path,
    });

    return {
      message: 'Virus scan started',
      jobId: job.id,
    };
  }

  async getStorageStats() {
    const totalSizeResult = await this.prisma.file.aggregate({
      _sum: {
        size: true,
      },
    });

    const filesByType = await this.prisma.file.groupBy({
      by: ['type'],
      _count: {
        _all: true,
      },
      _sum: {
        size: true,
      },
    }).then(results => results.map(r => ({ 
      type: r.type, 
      count: r._count._all,
      totalSize: r._sum.size || 0
    })));

    const filesByStatus = await this.prisma.file.groupBy({
      by: ['status'],
      _count: {
        _all: true,
      },
    }).then(results => results.map(r => ({ 
      status: r.status, 
      count: r._count._all
    })));

    return {
      totalSize: totalSizeResult._sum.size || 0,
      filesByType,
      filesByStatus,
      storageProvider: this.storageService.getCurrentProvider(),
    };
  }

  private async queueFileProcessing(fileId: string): Promise<void> {
    await this.fileQueue.add(
      'initial-processing',
      {
        fileId,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );
  }

  private async cleanupTempFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      this.logger.warn(`Failed to cleanup temp file: ${filePath}`, error);
    }
  }

  async processChunk(data: {
    uploadId: string;
    chunkIndex: number;
    totalChunks: number;
    chunk: Express.Multer.File;
    userId: string;
  }): Promise<{
    success: boolean;
    completed: boolean;
    uploadId: string;
    fileId?: string;
  }> {
    // TODO: Implement chunk processing logic
    // This is a stub implementation for now
    const { uploadId, chunkIndex, totalChunks } = data;

    this.logger.log(
      `Processing chunk ${chunkIndex + 1}/${totalChunks} for upload ${uploadId}`,
    );

    // For now, just return success and check if it's the last chunk
    const completed = chunkIndex === totalChunks - 1;

    return {
      success: true,
      completed,
      uploadId,
      fileId: completed ? `file_${uploadId}` : undefined,
    };
  }

  // Additional methods for maintenance processor
  async exists(fileId: string): Promise<boolean> {
    const file = await this.prisma.file.findUnique({ where: { id: fileId } });
    return !!file;
  }

  async findRecent(limit: number = 20): Promise<File[]> {
    return this.prisma.file.findMany({
      orderBy: { createdAt: 'desc' },
      take: limit,
    });
  }

  async findOne(id: string): Promise<File> {
    const file = await this.prisma.file.findUnique({
      where: { id },
      include: { versions: true },
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    return file;
  }
}
