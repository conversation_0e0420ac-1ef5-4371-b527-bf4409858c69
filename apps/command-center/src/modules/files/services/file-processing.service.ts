import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs/promises';

import { File } from '../../../common/types/prisma.types';
import { StorageService } from './storage.service';
import { ImageProcessingService } from './image-processing.service';
import { VideoProcessingService } from './video-processing.service';
import { AudioProcessingService } from './audio-processing.service';
import { DocumentProcessingService } from './document-processing.service';
import { VirusScanningService } from './virus-scanning.service';

export interface ProcessingResult {
  success: boolean;
  errors: string[];
  metadata?: any;
  variants?: Record<string, any>;
  processingTime: number;
}

export interface ProcessingOptions {
  skipVirusScan?: boolean;
  generateThumbnails?: boolean;
  extractText?: boolean;
  optimize?: boolean;
  customOptions?: Record<string, any>;
}

@Injectable()
export class FileProcessingService {
  private readonly logger = new Logger(FileProcessingService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly storageService: StorageService,
    private readonly imageProcessingService: ImageProcessingService,
    private readonly videoProcessingService: VideoProcessingService,
    private readonly audioProcessingService: AudioProcessingService,
    private readonly documentProcessingService: DocumentProcessingService,
    private readonly virusScanningService: VirusScanningService,
  ) {}

  async processFile(
    fileId: string,
    options: ProcessingOptions = {},
  ): Promise<ProcessingResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let metadata: any = {};
    let variants: Record<string, any> = {};

    try {
      // Get file from database
      const file = await this.prisma.file.findUnique({ where: { id: fileId } });
      if (!file) {
        throw new Error(`File not found: ${fileId}`);
      }

      // Update status to processing
      await this.prisma.file.update({
        where: { id: fileId },
        data: { status: 'PROCESSING' },
      });

      // Virus scanning
      if (!options.skipVirusScan && this.virusScanningService.isAvailable()) {
        const scanResult = await this.virusScanningService.scanFile(
          file.storagePath,
        );

        file.processingData = {
          ...file.processingData,
          virusScanned: true,
          virusScanDate: scanResult.scannedAt,
          virusScanStatus: scanResult.isInfected ? 'infected' : 'clean',
          virusScanResult: scanResult.isInfected ? 'infected' : 'clean',
        };

        if (scanResult.isInfected) {
          await this.prisma.file.update({
            where: { id: fileId },
            data: { status: 'QUARANTINED' },
          });

          return {
            success: false,
            errors: [`Virus detected: ${scanResult.viruses.join(', ')}`],
            processingTime: Date.now() - startTime,
          };
        }
      }

      // Process based on file type
      switch (file.type) {
        case 'IMAGE':
          const imageResult = await this.processImage(file, options);
          metadata = { ...metadata, ...imageResult.metadata };
          variants = { ...variants, ...imageResult.variants };
          errors.push(...imageResult.errors);
          break;

        case 'VIDEO':
          const videoResult = await this.processVideo(file, options);
          metadata = { ...metadata, ...videoResult.metadata };
          variants = { ...variants, ...videoResult.variants };
          errors.push(...videoResult.errors);
          break;

        case 'AUDIO':
          const audioResult = await this.processAudio(file, options);
          metadata = { ...metadata, ...audioResult.metadata };
          variants = { ...variants, ...audioResult.variants };
          errors.push(...audioResult.errors);
          break;

        case 'DOCUMENT':
          const documentResult = await this.processDocument(file, options);
          metadata = { ...metadata, ...documentResult.metadata };
          variants = { ...variants, ...documentResult.variants };
          errors.push(...documentResult.errors);
          break;

        default:
          this.logger.warn(
            `No specific processing for file type: ${file.type}`,
          );
      }

      // Update file with processing results
      await this.prisma.file.update({
        where: { id: fileId },
        data: {
          status: errors.length === 0 ? 'READY' : 'FAILED',
          metadata: {
            ...(file.metadata as any || {}),
            ...metadata,
            variants: {
              ...(file.metadata as any)?.variants,
              ...variants,
            },
            processingData: {
              ...(file.metadata as any)?.processingData,
              processedAt: new Date(),
              processingDuration: Date.now() - startTime,
              processingErrors: errors,
            },
          },
        },
      });

      return {
        success: errors.length === 0,
        errors,
        metadata,
        variants,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      this.logger.error(`File processing failed for ${fileId}:`, error);

      // Update file status to failed
      try {
        await this.prisma.file.update({
          where: { id: fileId },
          data: {
            status: 'FAILED',
            metadata: {
              processingData: {
                processedAt: new Date().toISOString(),
                processingDuration: Date.now() - startTime,
                processingErrors: [error.message],
              },
            },
          },
        });
      } catch (updateError) {
        this.logger.error('Failed to update file status:', updateError);
      }

      return {
        success: false,
        errors: [error.message],
        processingTime: Date.now() - startTime,
      };
    }
  }

  private async processImage(
    file: File,
    options: ProcessingOptions,
  ): Promise<{ metadata: any; variants: any; errors: string[] }> {
    const errors: string[] = [];
    const metadata: any = {};
    const variants: any = {};

    try {
      // Get image buffer
      const stream = await this.storageService.retrieve(file.storagePath);
      const buffer = await this.streamToBuffer(stream);

      // Extract metadata
      const imageMetadata =
        await this.imageProcessingService.getImageMetadata(buffer);
      Object.assign(metadata, imageMetadata);

      // Generate thumbnails
      if (options.generateThumbnails !== false) {
        try {
          const thumbnails =
            await this.imageProcessingService.generateThumbnails(buffer);

          for (const [size, result] of thumbnails) {
            const storageResult = await this.storageService.store(
              {
                buffer: result.buffer,
                size: result.size,
                mimeType: result.format,
                originalName: `${file.filename}_thumb_${size}.${result.format}`,
              },
              {
                directory: `thumbnails/${file.id}`,
              },
            );

            variants[`thumbnail_${size}`] = {
              path: storageResult.path,
              size: result.size,
              mimeType: result.format,
              width: result.metadata.width,
              height: result.metadata.height,
              quality: result.quality,
            };
          }

          file.thumbnailUrl = variants.thumbnail_medium?.path;
        } catch (error) {
          errors.push(`Failed to generate thumbnails: ${error.message}`);
        }
      }

      // Optimize image if requested
      if (options.optimize) {
        try {
          const optimized =
            await this.imageProcessingService.optimizeImage(buffer);
          const storageResult = await this.storageService.store(
            {
              buffer: optimized.buffer,
              size: optimized.size,
              mimeType: optimized.format,
              originalName: `${file.filename}_optimized.${optimized.format}`,
            },
            {
              directory: `optimized/${file.id}`,
            },
          );

          variants.optimized = {
            path: storageResult.path,
            size: optimized.size,
            mimeType: optimized.format,
            quality: optimized.quality,
          };
        } catch (error) {
          errors.push(`Failed to optimize image: ${error.message}`);
        }
      }

      // Extract colors
      try {
        const colors = await this.imageProcessingService.extractColors(buffer);
        metadata.dominantColors = colors;
      } catch (error) {
        // Non-critical error
        this.logger.debug(`Failed to extract colors: ${error.message}`);
      }
    } catch (error) {
      errors.push(`Image processing failed: ${error.message}`);
    }

    return { metadata, variants, errors };
  }

  private async processVideo(
    file: File,
    options: ProcessingOptions,
  ): Promise<{ metadata: any; variants: any; errors: string[] }> {
    const errors: string[] = [];
    const metadata: any = {};
    const variants: any = {};

    try {
      // Extract metadata
      const videoMetadata = await this.videoProcessingService.getVideoMetadata(
        file.storagePath,
      );
      Object.assign(metadata, videoMetadata);

      // Generate thumbnails
      if (options.generateThumbnails !== false) {
        try {
          const thumbnailPaths =
            await this.videoProcessingService.generateThumbnails(
              file.storagePath,
              {
                count: 3,
                size: '320x240',
              },
            );

          for (let i = 0; i < thumbnailPaths.length; i++) {
            const buffer = await fs.readFile(thumbnailPaths[i]);
            const storageResult = await this.storageService.store(
              {
                buffer,
                size: buffer.length,
                mimeType: 'image/jpeg',
                originalName: `${file.filename}_thumb_${i}.jpg`,
              },
              {
                directory: `thumbnails/${file.id}`,
              },
            );

            variants[`thumbnail_${i}`] = {
              path: storageResult.path,
              size: buffer.length,
              mimeType: 'image/jpeg',
            };

            // Clean up temp file
            await fs.unlink(thumbnailPaths[i]).catch(() => {});
          }

          // Set the first thumbnail as the main thumbnail
          if (variants.thumbnail_0) {
            file.thumbnailUrl = variants.thumbnail_0.path;
          }
        } catch (error) {
          errors.push(`Failed to generate video thumbnails: ${error.message}`);
        }
      }

      // Compress video if requested
      if (options.optimize) {
        try {
          const compressedPath =
            await this.videoProcessingService.compressVideo(file.storagePath);
          const buffer = await fs.readFile(compressedPath);

          const storageResult = await this.storageService.store(
            {
              buffer,
              size: buffer.length,
              mimeType: 'video/mp4',
              originalName: `${file.filename}_compressed.mp4`,
            },
            {
              directory: `compressed/${file.id}`,
            },
          );

          variants.compressed = {
            path: storageResult.path,
            size: buffer.length,
            mimeType: 'video/mp4',
          };

          // Clean up temp file
          await fs.unlink(compressedPath).catch(() => {});
        } catch (error) {
          errors.push(`Failed to compress video: ${error.message}`);
        }
      }
    } catch (error) {
      errors.push(`Video processing failed: ${error.message}`);
    }

    return { metadata, variants, errors };
  }

  private async processAudio(
    file: File,
    options: ProcessingOptions,
  ): Promise<{ metadata: any; variants: any; errors: string[] }> {
    const errors: string[] = [];
    const metadata: any = {};
    const variants: any = {};

    try {
      // Extract metadata
      const audioMetadata = await this.audioProcessingService.getAudioMetadata(
        file.storagePath,
      );
      Object.assign(metadata, audioMetadata);

      // Generate waveform
      if (options.generateThumbnails !== false) {
        try {
          const waveformPath =
            await this.audioProcessingService.generateWaveform(
              file.storagePath,
            );
          const buffer = await fs.readFile(waveformPath);

          const storageResult = await this.storageService.store(
            {
              buffer,
              size: buffer.length,
              mimeType: 'image/png',
              originalName: `${file.filename}_waveform.png`,
            },
            {
              directory: `waveforms/${file.id}`,
            },
          );

          variants.waveform = {
            path: storageResult.path,
            size: buffer.length,
            mimeType: 'image/png',
          };

          file.thumbnailUrl = variants.waveform.path;

          // Clean up temp file
          await fs.unlink(waveformPath).catch(() => {});
        } catch (error) {
          errors.push(`Failed to generate waveform: ${error.message}`);
        }
      }

      // Compress audio if requested
      if (options.optimize) {
        try {
          const compressedPath =
            await this.audioProcessingService.compressAudio(file.storagePath);
          const buffer = await fs.readFile(compressedPath);

          const storageResult = await this.storageService.store(
            {
              buffer,
              size: buffer.length,
              mimeType: 'audio/mpeg',
              originalName: `${file.filename}_compressed.mp3`,
            },
            {
              directory: `compressed/${file.id}`,
            },
          );

          variants.compressed = {
            path: storageResult.path,
            size: buffer.length,
            mimeType: 'audio/mpeg',
          };

          // Clean up temp file
          await fs.unlink(compressedPath).catch(() => {});
        } catch (error) {
          errors.push(`Failed to compress audio: ${error.message}`);
        }
      }
    } catch (error) {
      errors.push(`Audio processing failed: ${error.message}`);
    }

    return { metadata, variants, errors };
  }

  private async processDocument(
    file: File,
    options: ProcessingOptions,
  ): Promise<{ metadata: any; variants: any; errors: string[] }> {
    const errors: string[] = [];
    const metadata: any = {};
    const variants: any = {};

    try {
      // Extract text and metadata
      if (options.extractText !== false) {
        try {
          const extractionResult =
            await this.documentProcessingService.extractText(
              file.storagePath,
              file.mimeType,
            );

          metadata.documentMetadata = extractionResult.metadata;
          metadata.extractedText = extractionResult.text.substring(0, 1000); // Store first 1000 chars

          file.processingData = {
            ...file.processingData,
            ocrProcessed: true,
            ocrText: extractionResult.text,
          };

          // Store full text as a separate file
          const textBuffer = Buffer.from(extractionResult.text, 'utf-8');
          const storageResult = await this.storageService.store(
            {
              buffer: textBuffer,
              size: textBuffer.length,
              mimeType: 'text/plain',
              originalName: `${file.filename}_extracted.txt`,
            },
            {
              directory: `extracted/${file.id}`,
            },
          );

          variants.extractedText = {
            path: storageResult.path,
            size: textBuffer.length,
            mimeType: 'text/plain',
          };
        } catch (error) {
          errors.push(`Failed to extract text: ${error.message}`);
        }
      }

      // Generate preview for PDFs
      if (
        file.mimeType === 'application/pdf' &&
        options.generateThumbnails !== false
      ) {
        try {
          // Note: This is a placeholder - actual implementation would generate a real preview
          this.logger.warn('PDF preview generation not implemented');
        } catch (error) {
          errors.push(`Failed to generate PDF preview: ${error.message}`);
        }
      }

      // Compress PDF if requested
      if (file.mimeType === 'application/pdf' && options.optimize) {
        try {
          const compressedPath =
            await this.documentProcessingService.compressPDF(file.storagePath);
          const buffer = await fs.readFile(compressedPath);

          const storageResult = await this.storageService.store(
            {
              buffer,
              size: buffer.length,
              mimeType: 'application/pdf',
              originalName: `${file.filename}_compressed.pdf`,
            },
            {
              directory: `compressed/${file.id}`,
            },
          );

          variants.compressed = {
            path: storageResult.path,
            size: buffer.length,
            mimeType: 'application/pdf',
          };

          // Clean up temp file
          await fs.unlink(compressedPath).catch(() => {});
        } catch (error) {
          errors.push(`Failed to compress PDF: ${error.message}`);
        }
      }
    } catch (error) {
      errors.push(`Document processing failed: ${error.message}`);
    }

    return { metadata, variants, errors };
  }

  private async streamToBuffer(stream: any): Promise<Buffer> {
    if (Buffer.isBuffer(stream)) {
      return stream;
    }

    const chunks: Buffer[] = [];
    for await (const chunk of stream) {
      chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
    }
    return Buffer.concat(chunks);
  }
}
