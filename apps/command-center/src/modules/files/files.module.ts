import { Module } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { BullModule } from '@nestjs/bull';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as multer from 'multer';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Common Module for AppGateway
import { CommonModule } from '../../common/common.module';


// Controllers
import { FilesController } from './files.controller';

// Services
import { FilesService } from './files.service';
import {
  StorageService,
  LocalStorageProvider,
  MinioStorageProvider,
} from './services/storage.service';
import { FileValidationService } from './services/file-validation.service';
import { ImageProcessingService } from './services/image-processing.service';
import { VideoProcessingService } from './services/video-processing.service';
import { AudioProcessingService } from './services/audio-processing.service';
import { DocumentProcessingService } from './services/document-processing.service';
import { VirusScanningService } from './services/virus-scanning.service';
import { FileProcessingService } from './services/file-processing.service';

// Queue Processors
import { FileProcessor } from '../queue/processors/file.processor';

@Module({
  imports: [
    CommonModule, // Import CommonModule to access AppGateway
    PrismaModule,
    BullModule.registerQueue({
      name: 'file-processing',
    }),
    MulterModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        storage: multer.diskStorage({
          destination: async (req, file, cb) => {
            const uploadPath =
              configService.get('fileStorage.local.tempPath') ||
              './uploads/temp';
            cb(null, uploadPath);
          },
          filename: (req, file, cb) => {
            const uniqueSuffix = `${uuidv4()}${path.extname(file.originalname)}`;
            cb(null, uniqueSuffix);
          },
        }),
        limits: {
          fileSize:
            configService.get('fileStorage.maxFileSize') || 100 * 1024 * 1024, // 100MB default
        },
        fileFilter: (req, file, cb) => {
          const allowedMimeTypes =
            configService.get('fileStorage.allowedMimeTypes') || [];
          if (allowedMimeTypes.includes(file.mimetype)) {
            cb(null, true);
          } else {
            cb(new Error(`File type ${file.mimetype} is not allowed`), false);
          }
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [FilesController],
  providers: [
    FilesService,
    StorageService,
    LocalStorageProvider,
    MinioStorageProvider,
    FileValidationService,
    ImageProcessingService,
    VideoProcessingService,
    AudioProcessingService,
    DocumentProcessingService,
    VirusScanningService,
    FileProcessingService,
    FileProcessor,
  ],
  exports: [
    FilesService,
    StorageService,
    FileValidationService,
    ImageProcessingService,
    VideoProcessingService,
    AudioProcessingService,
    DocumentProcessingService,
    FileProcessingService,
  ],
})
export class FilesModule {}
