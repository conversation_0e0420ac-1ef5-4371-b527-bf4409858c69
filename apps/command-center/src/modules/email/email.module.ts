import { Module } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { BullModule } from '@nestjs/bull';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Services
import { EmailService } from './services/email.service';
import { EmailSyncService } from './services/email-sync.service';

// Providers
import { GmailProvider } from './providers/gmail.provider';
import { OutlookProvider } from './providers/outlook.provider';
import { EmailProviderFactory } from './providers/email-provider.factory';

// Controllers
import { EmailController } from './controllers/email.controller';

// Processors
import { EmailSyncProcessor } from './processors/email-sync.processor';

// Gateways
import { EmailSyncGateway } from './gateways/email-sync.gateway';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    PrismaModule,
    BullModule.registerQueue({
      name: 'email-sync',
      defaultJobOptions: {
        removeOnComplete: 50,
        removeOnFail: 20,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    }),
  ],
  controllers: [EmailController],
  providers: [
    EmailService,
    EmailSyncService,
    GmailProvider,
    OutlookProvider,
    EmailProviderFactory,
    EmailSyncProcessor,
    EmailSyncGateway,
  ],
  exports: [
    EmailService,
    EmailSyncService,
    GmailProvider,
    OutlookProvider,
    EmailProviderFactory,
  ],
})
export class EmailModule {}
