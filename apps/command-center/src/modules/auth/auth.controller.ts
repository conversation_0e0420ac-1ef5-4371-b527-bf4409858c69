import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Get,
  Patch,
  Query,
  Req,
  Res,
  BadRequestException,
} from '@nestjs/common';
import { Response } from 'express';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import {
  AuthService,
  LoginCredentials,
  RegisterData,
  PasswordResetRequest,
} from './services/auth.service';
import { TokenService } from './services/token.service';
import { CurrentUser } from './decorators/current-user.decorator';
import { User } from '../../common/types/prisma.types';
import { SanitizationPipe } from '../../common/validators/sanitization.pipe';

export class LoginDto {
  email: string;
  password: string;
  device_id?: string;
}

export class RegisterDto {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export class RefreshTokenDto {
  refresh_token: string;
}

export class PasswordResetRequestDto {
  email: string;
}

export class PasswordResetConfirmDto {
  token: string;
  new_password: string;
}

export class ChangePasswordDto {
  current_password: string;
  new_password: string;
}

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly tokenService: TokenService,
  ) {}

  @Post('login')
  @UseGuards(AuthGuard('local'))
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Request() req: any, @Res({ passthrough: true }) res: Response) {
    const { user, tokens, sessionId, requiresVerification } = req.user;

    // Set secure HTTP-only cookies for tokens
    res.cookie('access_token', tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: tokens.expiresIn * 1000,
    });

    res.cookie('refresh_token', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role || '',
        isEmailVerified: user.isEmailVerified,
      },
      tokens,
      sessionId,
      requiresVerification,
    };
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'User registration' })
  @ApiResponse({ status: 201, description: 'Registration successful' })
  @ApiResponse({
    status: 400,
    description: 'Invalid input or user already exists',
  })
  async register(
    @Body(new SanitizationPipe()) body: RegisterDto,
    @Req() req: any,
  ) {
    const deviceInfo = {
      userAgent: req.headers['user-agent'],
      ipAddress: this.extractIpAddress(req),
    };

    const registerData: RegisterData = {
      ...body,
      deviceInfo,
    };

    const result = await this.authService.register(registerData);

    return {
      user: {
        id: result.user.id,
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        isEmailVerified: result.user.isEmailVerified,
      },
      requiresEmailVerification: result.requiresEmailVerification,
    };
  }

  @Post('refresh')
  @UseGuards(AuthGuard('jwt-refresh'))
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refresh(
    @Request() req: any,
    @Res({ passthrough: true }) res: Response,
  ) {
    const { refreshToken, deviceInfo } = req.user;

    const tokens = await this.authService.refreshTokens(
      refreshToken,
      deviceInfo,
    );

    // Update access token cookie
    res.cookie('access_token', tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: tokens.expiresIn * 1000,
    });

    return { tokens };
  }

  @Post('logout')
  @UseGuards(AuthGuard('jwt'))
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async logout(
    @CurrentUser() user: User,
    @Body() body: { refresh_token?: string },
    @Req() req: any,
    @Res({ passthrough: true }) res: Response,
  ) {
    const sessionId = (user as any).sessionId;
    const refreshToken = body.refresh_token || req.cookies?.refresh_token;

    await this.authService.logout(sessionId, refreshToken);

    // Clear cookies
    res.clearCookie('access_token');
    res.clearCookie('refresh_token');

    return { message: 'Logout successful' };
  }

  @Post('logout-all')
  @UseGuards(AuthGuard('jwt'))
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from all devices' })
  @ApiResponse({ status: 200, description: 'Logged out from all devices' })
  async logoutAll(
    @CurrentUser() user: User,
    @Res({ passthrough: true }) res: Response,
  ) {
    const currentSessionId = (user as any).sessionId;

    // Revoke all refresh tokens except current session
    const revokedCount = await this.tokenService.revokeAllUserTokens(
      user.id.toString(),
      'Logout from all devices',
    );

    // Clear cookies for current session
    res.clearCookie('access_token');
    res.clearCookie('refresh_token');

    return {
      message: 'Logged out from all devices',
      sessionsTerminated: revokedCount,
    };
  }

  @Post('password-reset/request')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({
    status: 200,
    description: 'Password reset email sent if user exists',
  })
  async requestPasswordReset(
    @Body(new SanitizationPipe()) body: PasswordResetRequestDto,
    @Req() req: any,
  ) {
    const request: PasswordResetRequest = {
      email: body.email,
      ipAddress: this.extractIpAddress(req),
      userAgent: req.headers['user-agent'],
    };

    await this.authService.requestPasswordReset(request);

    return {
      message: 'If the email exists, a password reset link has been sent',
    };
  }

  @Post('password-reset/confirm')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Confirm password reset' })
  @ApiResponse({ status: 200, description: 'Password reset successful' })
  @ApiResponse({ status: 400, description: 'Invalid token or weak password' })
  async confirmPasswordReset(
    @Body(new SanitizationPipe()) body: PasswordResetConfirmDto,
    @Req() req: any,
  ) {
    await this.authService.confirmPasswordReset({
      token: body.token,
      newPassword: body.new_password,
      ipAddress: this.extractIpAddress(req),
      userAgent: req.headers['user-agent'],
    });

    return { message: 'Password reset successful' };
  }

  @Get('verify-email')
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  async verifyEmail(@Query('token') token: string, @Req() req: any) {
    if (!token) {
      throw new BadRequestException('Verification token is required');
    }

    await this.authService.verifyEmail(token, this.extractIpAddress(req));

    return { message: 'Email verified successfully' };
  }

  @Post('resend-verification')
  @UseGuards(AuthGuard('jwt'))
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Resend email verification' })
  @ApiResponse({ status: 200, description: 'Verification email sent' })
  async resendVerification(@CurrentUser() user: User, @Req() req: any) {
    if (user.isEmailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    await this.authService.sendEmailVerification(
      user,
      this.extractIpAddress(req),
    );

    return { message: 'Verification email sent' };
  }

  @Patch('change-password')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Change password' })
  @ApiResponse({ status: 200, description: 'Password changed successfully' })
  @ApiResponse({
    status: 400,
    description: 'Invalid current password or weak new password',
  })
  async changePassword(
    @CurrentUser() user: User,
    @Body(new SanitizationPipe()) body: ChangePasswordDto,
  ) {
    await this.authService.changePassword(
      user.id,
      body.current_password,
      body.new_password,
    );

    return { message: 'Password changed successfully' };
  }

  @Get('me')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'User profile retrieved' })
  async getProfile(@CurrentUser() user: User) {
    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isEmailVerified: user.isEmailVerified,
        isActive: user.isActive,
        role: user.role || '',
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
      },
    };
  }

  @Get('sessions')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user active sessions' })
  @ApiResponse({ status: 200, description: 'Active sessions retrieved' })
  async getUserSessions(@CurrentUser() user: User) {
    const sessions = await this.tokenService.getUserActiveTokens(user.id.toString());

    return {
      sessions: sessions.map((token) => ({
        id: token.id,
        createdAt: token.createdAt,
        expiresAt: token.expiresAt,
        isCurrent: (token as any).metadata?.sessionId === (user as any).sessionId,
      })),
    };
  }

  // Developer authentication endpoints
  @Post('developer/login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Developer login with username/password' })
  @ApiResponse({ status: 200, description: 'Developer login successful' })
  @ApiResponse({ status: 401, description: 'Invalid developer credentials' })
  async developerLogin(
    @Body() body: { username: string; password: string },
    @Req() req: any,
    @Res({ passthrough: true }) res: Response,
  ) {
    // Simple developer authentication
    const devUsername = process.env.DEV_USERNAME || 'developer';
    const devPassword = process.env.DEV_PASSWORD || 'dev123456';

    if (body.username !== devUsername || body.password !== devPassword) {
      throw new BadRequestException('Invalid developer credentials');
    }

    // Create a special developer token
    const devPayload = {
      sub: 'developer',
      email: '<EMAIL>',
      roles: ['developer'],
      permissions: ['*'],
      type: 'developer',
    };

    const devToken = await this.tokenService.signCustomToken(devPayload, {
      expiresIn: '8h',
      issuer: 'command-center-dev',
    });

    res.cookie('dev_token', devToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 8 * 60 * 60 * 1000, // 8 hours
    });

    return {
      message: 'Developer login successful',
      token: devToken,
      user: {
        username: devUsername,
        type: 'developer',
        permissions: ['*'],
      },
    };
  }

  private extractIpAddress(request: any): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      'unknown'
    );
  }
}
