import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { RefreshToken, User } from '../../../common/types/prisma.types';
import { CryptoService } from './crypto.service';

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface JwtPayload {
  sub: string; // User ID
  email: string;
  roles: string[];
  permissions: string[];
  sessionId: string;
  deviceId?: string;
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
  jti?: string;
}

@Injectable()
export class TokenService {
  private readonly logger = new Logger(TokenService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly cryptoService: CryptoService,
    private readonly prisma: PrismaService,
  ) {}

  async generateTokenPair(
    user: User,
    deviceInfo?: {
      deviceId?: string;
      userAgent?: string;
      ipAddress?: string;
    },
  ): Promise<TokenPair> {
    const sessionId = this.cryptoService.generateSecureToken(16);
    const jti = this.cryptoService.generateSecureToken(16);

    // Create JWT payload
    const payload: JwtPayload = {
      sub: user.id.toString(),
      email: user.email,
      roles: user.roles || [],
      permissions: this.extractPermissions(user),
      sessionId,
      deviceId: deviceInfo?.deviceId,
      jti,
    };

    // Generate access token
    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn: this.configService.get('JWT_EXPIRES_IN', '15m'),
      issuer: this.configService.get('JWT_ISSUER', 'command-center'),
      audience: this.configService.get('JWT_AUDIENCE', 'command-center-api'),
    });

    // Generate refresh token
    const refreshTokenValue = this.cryptoService.generateSecureToken(64);
    const refreshTokenExpiry = new Date();
    refreshTokenExpiry.setDate(
      refreshTokenExpiry.getDate() +
        parseInt(this.configService.get('REFRESH_TOKEN_DAYS', '30')),
    );

    // Store refresh token in database
    await this.prisma.refreshToken.create({
      data: {
        token: refreshTokenValue,
        userId: user.id,
        expiresAt: refreshTokenExpiry,
        deviceId: deviceInfo?.deviceId,
        userAgent: deviceInfo?.userAgent,
        ipAddress: deviceInfo?.ipAddress,
      },
    });

    const expiresIn = parseInt(
      this.configService.get('JWT_EXPIRES_IN_SECONDS', '900'),
    );

    return {
      accessToken,
      refreshToken: refreshTokenValue,
      expiresIn,
      tokenType: 'Bearer',
    };
  }

  async refreshTokens(
    refreshTokenValue: string,
    deviceInfo?: {
      userAgent?: string;
      ipAddress?: string;
    },
  ): Promise<TokenPair> {
    // Find refresh token
    const refreshToken = await this.prisma.refreshToken.findFirst({
      where: { token: refreshTokenValue },
      include: {
        user: true,
      },
    });

    if (
      !refreshToken ||
      refreshToken.revokedAt ||
      refreshToken.expiresAt < new Date()
    ) {
      throw new Error('Invalid or expired refresh token');
    }

    // Update IP address if provided (we can't update lastUsedAt as it doesn't exist in schema)
    if (deviceInfo?.ipAddress) {
      await this.prisma.refreshToken.update({
        where: { id: refreshToken.id },
        data: {
          ipAddress: deviceInfo.ipAddress,
        },
      });
    }

    // Generate new token pair
    return this.generateTokenPair(refreshToken.user, {
      deviceId: refreshToken.deviceId,
      userAgent: deviceInfo?.userAgent || refreshToken.userAgent,
      ipAddress: deviceInfo?.ipAddress || refreshToken.ipAddress,
    });
  }

  async revokeRefreshToken(
    refreshTokenValue: string,
    reason?: string,
    revokedBy?: string,
  ): Promise<void> {
    const refreshToken = await this.prisma.refreshToken.findFirst({
      where: { token: refreshTokenValue },
    });

    if (refreshToken) {
      await this.prisma.refreshToken.update({
        where: { id: refreshToken.id },
        data: {
          revokedAt: new Date(),
          revokedReason: reason,
        },
      });
    }
  }

  async revokeAllUserTokens(userId: string, reason?: string): Promise<number> {
    const result = await this.prisma.refreshToken.updateMany({
      where: {
        userId,
        revokedAt: null,
      },
      data: {
        revokedAt: new Date(),
        revokedReason: reason,
      },
    });

    return result.count;
  }

  async getUserActiveTokens(userId: string): Promise<RefreshToken[]> {
    return this.prisma.refreshToken.findMany({
      where: {
        userId,
        revokedAt: null,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async cleanupExpiredTokens(): Promise<number> {
    const result = await this.prisma.refreshToken.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });

    this.logger.log(`Cleaned up ${result.count} expired refresh tokens`);
    return result.count;
  }

  async verifyAccessToken(token: string): Promise<JwtPayload> {
    try {
      return await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('JWT_SECRET'),
        issuer: this.configService.get('JWT_ISSUER'),
        audience: this.configService.get('JWT_AUDIENCE'),
      });
    } catch (error) {
      throw new Error('Invalid or expired access token');
    }
  }

  async generatePasswordResetToken(userId: string): Promise<string> {
    const token = this.cryptoService.generateSecureToken(32);
    const payload = {
      sub: userId,
      type: 'password_reset',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };

    // Sign with different secret for password reset
    const resetSecret =
      this.configService.get('PASSWORD_RESET_SECRET') ||
      this.configService.get('JWT_SECRET');

    return this.jwtService.sign(payload, { secret: resetSecret });
  }

  async verifyPasswordResetToken(token: string): Promise<{ userId: string }> {
    try {
      const resetSecret =
        this.configService.get('PASSWORD_RESET_SECRET') ||
        this.configService.get('JWT_SECRET');

      const payload = await this.jwtService.verifyAsync(token, {
        secret: resetSecret,
      });

      if (payload.type !== 'password_reset') {
        throw new Error('Invalid token type');
      }

      return { userId: payload.sub };
    } catch (error) {
      throw new Error('Invalid or expired password reset token');
    }
  }

  async generateEmailVerificationToken(
    userId: string,
    email: string,
  ): Promise<string> {
    const payload = {
      sub: userId,
      email,
      type: 'email_verification',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 86400, // 24 hours
    };

    const verificationSecret =
      this.configService.get('EMAIL_VERIFICATION_SECRET') ||
      this.configService.get('JWT_SECRET');

    return this.jwtService.sign(payload, { secret: verificationSecret });
  }

  async verifyEmailVerificationToken(
    token: string,
  ): Promise<{ userId: string; email: string }> {
    try {
      const verificationSecret =
        this.configService.get('EMAIL_VERIFICATION_SECRET') ||
        this.configService.get('JWT_SECRET');

      const payload = await this.jwtService.verifyAsync(token, {
        secret: verificationSecret,
      });

      if (payload.type !== 'email_verification') {
        throw new Error('Invalid token type');
      }

      return { userId: payload.sub, email: payload.email };
    } catch (error) {
      throw new Error('Invalid or expired email verification token');
    }
  }

  private extractPermissions(user: User): string[] {
    // In the current schema, roles is a String[] array, not a relation
    // TODO: Implement proper role-to-permissions mapping when the relations are set up
    return user.roles || [];
  }

  // Token blacklisting for logged out tokens
  private readonly blacklistedTokens = new Set<string>();

  blacklistToken(jti: string): void {
    this.blacklistedTokens.add(jti);

    // Clean up old blacklisted tokens periodically
    if (this.blacklistedTokens.size > 10000) {
      this.blacklistedTokens.clear();
    }
  }

  isTokenBlacklisted(jti: string): boolean {
    return this.blacklistedTokens.has(jti);
  }

  // API Key generation for service-to-service communication
  generateApiKey(prefix: string = 'cc'): string {
    const key = this.cryptoService.generateSecureToken(32);
    return `${prefix}_${key}`;
  }

  verifyApiKey(apiKey: string): boolean {
    // In production, store API keys in database with proper hashing
    const validApiKeys = this.configService
      .get('VALID_API_KEYS', '')
      .split(',');
    return validApiKeys.includes(apiKey);
  }

  // Public method to sign custom tokens
  async signCustomToken(payload: any, options?: any): Promise<string> {
    return this.jwtService.signAsync(payload, options);
  }
}
