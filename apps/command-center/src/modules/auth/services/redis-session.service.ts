import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '../../redis/redis.service';
import { User } from '../../../common/types/prisma.types';

export interface SessionData {
  userId: string;
  email: string;
  roles: string[];
  permissions: string[];
  deviceId?: string;
  userAgent?: string;
  ipAddress: string;
  lastActivity: number;
  createdAt: number;
  expiresAt: number;
  isActive: boolean;
  loginMethod: 'password' | 'sso' | 'oauth' | 'api_key';
  deviceInfo?: {
    platform?: string;
    browser?: string;
    isMobile?: boolean;
  };
  metadata?: Record<string, any>;
}

export interface CrossAppSession {
  sessionId: string;
  userId: string;
  originApp: string;
  targetApps: string[];
  createdAt: number;
  lastActivity: number;
  expiresAt: number;
  sharedData?: Record<string, any>;
}

export interface SessionMetrics {
  totalActiveSessions: number;
  sessionsByApp: Record<string, number>;
  sessionsByUser: Record<string, number>;
  averageSessionDuration: number;
  peakConcurrentSessions: number;
  lastHourLogins: number;
}

@Injectable()
export class RedisSessionService {
  private readonly logger = new Logger(RedisSessionService.name);
  private readonly sessionPrefix = 'session:';
  private readonly userSessionsPrefix = 'user_sessions:';
  private readonly crossAppSessionPrefix = 'cross_app_session:';
  private readonly sessionMetricsKey = 'session_metrics';
  private readonly activeSessionsKey = 'active_sessions';
  private readonly defaultSessionTTL: number;
  private readonly maxSessionsPerUser: number;

  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {
    this.defaultSessionTTL = this.configService.get<number>(
      'SESSION_TTL_SECONDS',
      3600 * 24,
    ); // 24 hours
    this.maxSessionsPerUser = this.configService.get<number>(
      'MAX_SESSIONS_PER_USER',
      5,
    );
  }

  /**
   * Create a new session
   */
  async createSession(
    sessionId: string,
    user: User,
    loginInfo: {
      ipAddress: string;
      userAgent?: string;
      deviceId?: string;
      loginMethod?: 'password' | 'sso' | 'oauth' | 'api_key';
      deviceInfo?: {
        platform?: string;
        browser?: string;
        isMobile?: boolean;
      };
    },
    ttl?: number,
  ): Promise<SessionData> {
    const now = Date.now();
    const sessionTTL = ttl || this.defaultSessionTTL;

    const sessionData: SessionData = {
      userId: user.id.toString(),
      email: user.email,
      roles: user.role ? [user.role] : [],
      permissions: [],
      deviceId: loginInfo.deviceId,
      userAgent: loginInfo.userAgent,
      ipAddress: loginInfo.ipAddress,
      lastActivity: now,
      createdAt: now,
      expiresAt: now + sessionTTL * 1000,
      isActive: true,
      loginMethod: loginInfo.loginMethod || 'password',
      deviceInfo: loginInfo.deviceInfo,
      metadata: {},
    };

    // Store session data
    const sessionKey = this.getSessionKey(sessionId);
    await this.redisService.set(
      sessionKey,
      JSON.stringify(sessionData),
      sessionTTL,
    );

    // Add to user sessions set
    const userSessionsKey = this.getUserSessionsKey(user.id.toString());
    await this.redisService.sadd(userSessionsKey, sessionId);
    await this.redisService.expire(userSessionsKey, sessionTTL);

    // Add to active sessions
    await this.redisService.sadd(this.activeSessionsKey, sessionId);

    // Enforce session limits
    await this.enforceSessionLimits(user.id.toString());

    // Update metrics
    await this.updateSessionMetrics('created', user.id.toString());

    this.logger.log(`Created session ${sessionId} for user ${user.id}`);
    return sessionData;
  }

  /**
   * Get session data
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    const sessionKey = this.getSessionKey(sessionId);
    const sessionDataStr = await this.redisService.get<string>(sessionKey);

    if (!sessionDataStr) {
      return null;
    }

    try {
      const sessionData: SessionData = JSON.parse(sessionDataStr);

      // Check if session is expired
      if (sessionData.expiresAt < Date.now()) {
        await this.destroySession(sessionId);
        return null;
      }

      return sessionData;
    } catch (error) {
      this.logger.error(
        `Failed to parse session data for ${sessionId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(
    sessionId: string,
    newActivity?: {
      ipAddress?: string;
      userAgent?: string;
      metadata?: Record<string, any>;
    },
  ): Promise<boolean> {
    const sessionData = await this.getSession(sessionId);
    if (!sessionData) {
      return false;
    }

    // Update activity timestamp
    sessionData.lastActivity = Date.now();

    // Update additional info if provided
    if (newActivity) {
      if (newActivity.ipAddress) {
        sessionData.ipAddress = newActivity.ipAddress;
      }
      if (newActivity.userAgent) {
        sessionData.userAgent = newActivity.userAgent;
      }
      if (newActivity.metadata) {
        sessionData.metadata = {
          ...sessionData.metadata,
          ...newActivity.metadata,
        };
      }
    }

    // Store updated session
    const sessionKey = this.getSessionKey(sessionId);
    const ttl = Math.floor((sessionData.expiresAt - Date.now()) / 1000);

    if (ttl > 0) {
      await this.redisService.set(sessionKey, JSON.stringify(sessionData), ttl);
      return true;
    } else {
      await this.destroySession(sessionId);
      return false;
    }
  }

  /**
   * Extend session expiration
   */
  async extendSession(
    sessionId: string,
    additionalSeconds: number = 3600,
  ): Promise<boolean> {
    const sessionData = await this.getSession(sessionId);
    if (!sessionData) {
      return false;
    }

    // Extend expiration
    sessionData.expiresAt += additionalSeconds * 1000;
    sessionData.metadata = {
      ...sessionData.metadata,
      extended: true,
      extensionCount: (sessionData.metadata?.extensionCount || 0) + 1,
    };

    // Store updated session
    const sessionKey = this.getSessionKey(sessionId);
    const ttl = Math.floor((sessionData.expiresAt - Date.now()) / 1000);

    await this.redisService.set(sessionKey, JSON.stringify(sessionData), ttl);

    this.logger.log(
      `Extended session ${sessionId} by ${additionalSeconds} seconds`,
    );
    return true;
  }

  /**
   * Destroy a session
   */
  async destroySession(sessionId: string): Promise<boolean> {
    const sessionData = await this.getSession(sessionId);

    // Remove session data
    const sessionKey = this.getSessionKey(sessionId);
    await this.redisService.del(sessionKey);

    // Remove from user sessions
    if (sessionData) {
      const userSessionsKey = this.getUserSessionsKey(sessionData.userId);
      await this.redisService.srem(userSessionsKey, sessionId);
    }

    // Remove from active sessions
    await this.redisService.srem(this.activeSessionsKey, sessionId);

    // Update metrics
    if (sessionData) {
      await this.updateSessionMetrics('destroyed', sessionData.userId);
    }

    this.logger.log(`Destroyed session ${sessionId}`);
    return true;
  }

  /**
   * Get all sessions for a user
   */
  async getUserSessions(userId: string): Promise<SessionData[]> {
    const userSessionsKey = this.getUserSessionsKey(userId);
    const sessionIds = await this.redisService.smembers(userSessionsKey);

    const sessions: SessionData[] = [];

    for (const sessionId of sessionIds) {
      const sessionData = await this.getSession(sessionId);
      if (sessionData) {
        sessions.push(sessionData);
      }
    }

    return sessions.sort((a, b) => b.lastActivity - a.lastActivity);
  }

  /**
   * Destroy all sessions for a user
   */
  async destroyUserSessions(
    userId: string,
    excludeSessionId?: string,
  ): Promise<number> {
    const sessions = await this.getUserSessions(userId);
    let destroyedCount = 0;

    for (const session of sessions) {
      const sessionId = await this.findSessionIdByData(session);
      if (sessionId && sessionId !== excludeSessionId) {
        await this.destroySession(sessionId);
        destroyedCount++;
      }
    }

    this.logger.log(`Destroyed ${destroyedCount} sessions for user ${userId}`);
    return destroyedCount;
  }

  /**
   * Create cross-app session for SSO
   */
  async createCrossAppSession(
    sessionId: string,
    userId: string,
    originApp: string,
    targetApps: string[],
    sharedData?: Record<string, any>,
    ttl: number = 1800, // 30 minutes
  ): Promise<CrossAppSession> {
    const now = Date.now();

    const crossAppSession: CrossAppSession = {
      sessionId,
      userId,
      originApp,
      targetApps,
      createdAt: now,
      lastActivity: now,
      expiresAt: now + ttl * 1000,
      sharedData,
    };

    const key = this.getCrossAppSessionKey(sessionId);
    await this.redisService.set(key, JSON.stringify(crossAppSession), ttl);

    this.logger.log(
      `Created cross-app session ${sessionId} for user ${userId}`,
    );
    return crossAppSession;
  }

  /**
   * Get cross-app session
   */
  async getCrossAppSession(sessionId: string): Promise<CrossAppSession | null> {
    const key = this.getCrossAppSessionKey(sessionId);
    const sessionDataStr = await this.redisService.get<string>(key);

    if (!sessionDataStr) {
      return null;
    }

    try {
      const sessionData: CrossAppSession = JSON.parse(sessionDataStr);

      if (sessionData.expiresAt < Date.now()) {
        await this.redisService.del(key);
        return null;
      }

      return sessionData;
    } catch (error) {
      this.logger.error(
        `Failed to parse cross-app session ${sessionId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Update cross-app session activity
   */
  async updateCrossAppActivity(sessionId: string): Promise<boolean> {
    const sessionData = await this.getCrossAppSession(sessionId);
    if (!sessionData) {
      return false;
    }

    sessionData.lastActivity = Date.now();

    const key = this.getCrossAppSessionKey(sessionId);
    const ttl = Math.floor((sessionData.expiresAt - Date.now()) / 1000);

    if (ttl > 0) {
      await this.redisService.set(key, JSON.stringify(sessionData), ttl);
      return true;
    }

    return false;
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const activeSessionIds = await this.redisService.smembers(
      this.activeSessionsKey,
    );
    let cleanedCount = 0;

    for (const sessionId of activeSessionIds) {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) {
        // Session doesn't exist, remove from active sessions
        await this.redisService.srem(this.activeSessionsKey, sessionId);
        cleanedCount++;
      }
    }

    this.logger.log(`Cleaned up ${cleanedCount} expired sessions`);
    return cleanedCount;
  }

  /**
   * Get session metrics
   */
  async getSessionMetrics(): Promise<SessionMetrics> {
    const metricsStr = await this.redisService.get<string>(
      this.sessionMetricsKey,
    );

    if (metricsStr) {
      try {
        return JSON.parse(metricsStr);
      } catch (error) {
        this.logger.error(`Failed to parse session metrics: ${error.message}`);
      }
    }

    // Return default metrics
    return {
      totalActiveSessions: 0,
      sessionsByApp: {},
      sessionsByUser: {},
      averageSessionDuration: 0,
      peakConcurrentSessions: 0,
      lastHourLogins: 0,
    };
  }

  /**
   * Get active sessions count
   */
  async getActiveSessionsCount(): Promise<number> {
    const activeSessionIds = await this.redisService.smembers(
      this.activeSessionsKey,
    );
    return activeSessionIds.length;
  }

  /**
   * Check if session exists
   */
  async sessionExists(sessionId: string): Promise<boolean> {
    const sessionKey = this.getSessionKey(sessionId);
    const ttl = await this.redisService.ttl(sessionKey);
    return ttl > 0;
  }

  /**
   * Get sessions by IP address (security monitoring)
   */
  async getSessionsByIP(ipAddress: string): Promise<SessionData[]> {
    const activeSessionIds = await this.redisService.smembers(
      this.activeSessionsKey,
    );
    const sessions: SessionData[] = [];

    for (const sessionId of activeSessionIds) {
      const sessionData = await this.getSession(sessionId);
      if (sessionData && sessionData.ipAddress === ipAddress) {
        sessions.push(sessionData);
      }
    }

    return sessions;
  }

  /**
   * Invalidate sessions by criteria (security feature)
   */
  async invalidateSessionsByCriteria(criteria: {
    userId?: string;
    ipAddress?: string;
    deviceId?: string;
    beforeTimestamp?: number;
  }): Promise<number> {
    const activeSessionIds = await this.redisService.smembers(
      this.activeSessionsKey,
    );
    let invalidatedCount = 0;

    for (const sessionId of activeSessionIds) {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) continue;

      let shouldInvalidate = false;

      if (criteria.userId && sessionData.userId === criteria.userId) {
        shouldInvalidate = true;
      }
      if (criteria.ipAddress && sessionData.ipAddress === criteria.ipAddress) {
        shouldInvalidate = true;
      }
      if (criteria.deviceId && sessionData.deviceId === criteria.deviceId) {
        shouldInvalidate = true;
      }
      if (
        criteria.beforeTimestamp &&
        sessionData.createdAt < criteria.beforeTimestamp
      ) {
        shouldInvalidate = true;
      }

      if (shouldInvalidate) {
        await this.destroySession(sessionId);
        invalidatedCount++;
      }
    }

    this.logger.log(
      `Invalidated ${invalidatedCount} sessions matching criteria`,
    );
    return invalidatedCount;
  }

  private getSessionKey(sessionId: string): string {
    return `${this.sessionPrefix}${sessionId}`;
  }

  private getUserSessionsKey(userId: string): string {
    return `${this.userSessionsPrefix}${userId}`;
  }

  private getCrossAppSessionKey(sessionId: string): string {
    return `${this.crossAppSessionPrefix}${sessionId}`;
  }

  private async enforceSessionLimits(userId: string): Promise<void> {
    const sessions = await this.getUserSessions(userId);

    if (sessions.length > this.maxSessionsPerUser) {
      // Sort by last activity and remove oldest sessions
      const sortedSessions = sessions.sort(
        (a, b) => a.lastActivity - b.lastActivity,
      );
      const sessionsToRemove = sortedSessions.slice(
        0,
        sessions.length - this.maxSessionsPerUser,
      );

      for (const session of sessionsToRemove) {
        const sessionId = await this.findSessionIdByData(session);
        if (sessionId) {
          await this.destroySession(sessionId);
        }
      }
    }
  }

  private async findSessionIdByData(
    sessionData: SessionData,
  ): Promise<string | null> {
    // This is a simplified implementation - in production, you might want to maintain a reverse index
    const pattern = `${this.sessionPrefix}*`;
    const keys = await this.redisService.keys(pattern);

    for (const key of keys) {
      const data = await this.redisService.get<string>(key);
      if (data) {
        try {
          const parsed: SessionData = JSON.parse(data);
          if (
            parsed.userId === sessionData.userId &&
            parsed.createdAt === sessionData.createdAt
          ) {
            return key.replace(this.sessionPrefix, '');
          }
        } catch (error) {
          // Skip invalid session data
        }
      }
    }

    return null;
  }

  private async updateSessionMetrics(
    action: 'created' | 'destroyed',
    userId: string,
  ): Promise<void> {
    try {
      const metrics = await this.getSessionMetrics();

      if (action === 'created') {
        metrics.totalActiveSessions = await this.getActiveSessionsCount();
        metrics.sessionsByUser[userId] =
          (metrics.sessionsByUser[userId] || 0) + 1;
        metrics.lastHourLogins += 1;

        if (metrics.totalActiveSessions > metrics.peakConcurrentSessions) {
          metrics.peakConcurrentSessions = metrics.totalActiveSessions;
        }
      } else if (action === 'destroyed') {
        metrics.totalActiveSessions = Math.max(
          0,
          metrics.totalActiveSessions - 1,
        );
        metrics.sessionsByUser[userId] = Math.max(
          0,
          (metrics.sessionsByUser[userId] || 1) - 1,
        );
      }

      await this.redisService.set(
        this.sessionMetricsKey,
        JSON.stringify(metrics),
        3600,
      );
    } catch (error) {
      this.logger.error(`Failed to update session metrics: ${error.message}`);
    }
  }
}
