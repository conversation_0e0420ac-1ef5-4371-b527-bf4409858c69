import {
  Injectable,
  Logger,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { User } from '../../../common/types/prisma.types';
import { CryptoService } from './crypto.service';
import { TokenService, TokenPair } from './token.service';
import { SecurityService } from './security.service';
import { SessionService } from './session.service';
import {
  AuthAttemptType,
  AuthAttemptResult,
  EmailVerificationToken,
  PasswordResetToken,
} from '../../../common/types/prisma.types';

export interface LoginCredentials {
  email: string;
  password: string;
  deviceInfo?: {
    deviceId?: string;
    userAgent?: string;
    ipAddress: string;
  };
}

export interface LoginResult {
  user: User;
  tokens: TokenPair;
  requiresVerification?: boolean;
  sessionId: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  deviceInfo?: {
    userAgent?: string;
    ipAddress: string;
  };
}

export interface PasswordResetRequest {
  email: string;
  ipAddress: string;
  userAgent?: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
  ipAddress: string;
  userAgent?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly cryptoService: CryptoService,
    private readonly tokenService: TokenService,
    private readonly securityService: SecurityService,
    private readonly sessionService: SessionService,
  ) {}

  async login(credentials: LoginCredentials): Promise<LoginResult> {
    const { email, password, deviceInfo } = credentials;

    // Security check before attempting login
    const securityCheck = await this.securityService.checkSecurityPolicy(
      AuthAttemptType.LOGIN,
      email,
      deviceInfo?.ipAddress || '',
      deviceInfo?.userAgent,
    );

    if (!securityCheck.isAllowed) {
      await this.securityService.recordAuthAttempt(
        AuthAttemptType.LOGIN,
        AuthAttemptResult.BLOCKED,
        email,
        deviceInfo?.ipAddress || '',
        deviceInfo?.userAgent,
        securityCheck.blockedReason,
      );
      throw new UnauthorizedException(
        securityCheck.blockedReason || 'Login blocked',
      );
    }

    try {
      // Find user with roles and permissions
      const user = await this.prisma.user.findUnique({
        where: { email },
        include: {
          roles: {
            include: {
              role: true,
            },
          },
          departments: {
            include: {
              department: true,
            },
          },
        },
      });

      if (!user) {
        await this.recordFailedAttempt(email, deviceInfo, 'User not found');
        throw new UnauthorizedException('Invalid credentials');
      }

      // Verify password
      const isPasswordValid = await this.cryptoService.verifyPassword(
        password,
        user.password,
      );
      if (!isPasswordValid) {
        await this.recordFailedAttempt(
          email,
          deviceInfo,
          'Invalid password',
          user.id,
        );
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check if user is active
      if (!user.isActive) {
        await this.recordFailedAttempt(
          email,
          deviceInfo,
          'Account inactive',
          user.id,
        );
        throw new UnauthorizedException('Account is inactive');
      }

      // Check if email is verified (if required)
      if (
        !user.isEmailVerified &&
        process.env.REQUIRE_EMAIL_VERIFICATION === 'true'
      ) {
        await this.recordFailedAttempt(
          email,
          deviceInfo,
          'Email not verified',
          user.id,
        );
        throw new UnauthorizedException('Email verification required');
      }

      // Generate session ID and tokens
      const sessionId = this.cryptoService.generateSecureToken(16);
      const tokens = await this.tokenService.generateTokenPair(user, {
        deviceId: deviceInfo?.deviceId,
        userAgent: deviceInfo?.userAgent,
        ipAddress: deviceInfo?.ipAddress,
      });

      // Create session
      await this.sessionService.createSession(user, sessionId, {
        deviceId: deviceInfo?.deviceId,
        userAgent: deviceInfo?.userAgent,
        ipAddress: deviceInfo?.ipAddress || '',
      });

      // Update user last login
      await this.prisma.user.update({
        where: { id: user.id },
        data: { updatedAt: new Date() },
      });

      // Record successful attempt
      await this.securityService.recordAuthAttempt(
        AuthAttemptType.LOGIN,
        AuthAttemptResult.SUCCESS,
        email,
        deviceInfo?.ipAddress || '',
        deviceInfo?.userAgent,
        undefined,
        user.id.toString(),
      );

      this.logger.log(`Successful login for user ${user.id.toString()} (${email})`);

      return {
        user,
        tokens,
        requiresVerification: securityCheck.requiresAdditionalVerification,
        sessionId,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('Login error:', error);
      await this.recordFailedAttempt(email, deviceInfo, 'System error');
      throw new UnauthorizedException('Login failed');
    }
  }

  async register(
    data: RegisterData,
  ): Promise<{ user: User; requiresEmailVerification: boolean }> {
    const { email, password, firstName, lastName, deviceInfo } = data;

    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });
    if (existingUser) {
      throw new BadRequestException('User already exists');
    }

    // Validate password strength
    const passwordStrength =
      this.cryptoService.validatePasswordStrength(password);
    if (!passwordStrength.isStrong) {
      throw new BadRequestException({
        message: 'Password does not meet requirements',
        feedback: passwordStrength.feedback,
      });
    }

    // Hash password
    const hashedPassword =
      await this.cryptoService.hashPasswordArgon2(password);

    // Create user
    const user = await this.prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name: `${firstName || ''} ${lastName || ''}`.trim() || email,
        firstName,
        lastName,
        isActive: true,
      },
    });

    // Send email verification if required
    let requiresEmailVerification = false;
    if (process.env.REQUIRE_EMAIL_VERIFICATION === 'true') {
      await this.sendEmailVerification(user, deviceInfo?.ipAddress || '');
      requiresEmailVerification = true;
    }

    this.logger.log(`User registered: ${user.id.toString()} (${email})`);

    return { user, requiresEmailVerification };
  }

  async logout(sessionId: string, refreshToken?: string): Promise<void> {
    // Terminate session
    await this.sessionService.terminateSession(sessionId, 'User logout');

    // Revoke refresh token
    if (refreshToken) {
      await this.tokenService.revokeRefreshToken(refreshToken, 'User logout');
    }

    this.logger.log(`User logged out, session: ${sessionId}`);
  }

  async refreshTokens(
    refreshTokenValue: string,
    deviceInfo?: { userAgent?: string; ipAddress?: string },
  ): Promise<TokenPair> {
    try {
      return await this.tokenService.refreshTokens(
        refreshTokenValue,
        deviceInfo,
      );
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async requestPasswordReset(request: PasswordResetRequest): Promise<void> {
    const { email, ipAddress, userAgent } = request;

    // Security check
    const securityCheck = await this.securityService.checkSecurityPolicy(
      AuthAttemptType.PASSWORD_RESET,
      email,
      ipAddress,
      userAgent,
    );

    if (!securityCheck.isAllowed) {
      await this.securityService.recordAuthAttempt(
        AuthAttemptType.PASSWORD_RESET,
        AuthAttemptResult.BLOCKED,
        email,
        ipAddress,
        userAgent,
        securityCheck.blockedReason,
      );
      throw new BadRequestException(
        securityCheck.blockedReason || 'Request blocked',
      );
    }

    const user = await this.prisma.user.findUnique({ where: { email } });
    if (!user) {
      // Don't reveal if user exists
      this.logger.log(
        `Password reset requested for non-existent email: ${email}`,
      );
      return;
    }

    // Generate reset token
    const tokenValue = this.cryptoService.generateSecureToken(32);
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1); // 1 hour expiry

    // Store reset token
    const resetToken = await this.prisma.passwordResetToken.create({
      data: {
        token: tokenValue,
        userId: user.id,
        expiresAt,
      },
    });

    // Record attempt
    await this.securityService.recordAuthAttempt(
      AuthAttemptType.PASSWORD_RESET,
      AuthAttemptResult.SUCCESS,
      email,
      ipAddress,
      userAgent,
      undefined,
      user.id.toString(),
    );

    // TODO: Send password reset email
    this.logger.log(`Password reset token generated for user ${user.id.toString()}`);
  }

  async confirmPasswordReset(request: PasswordResetConfirm): Promise<void> {
    const { token, newPassword, ipAddress, userAgent } = request;

    const resetToken = await this.prisma.passwordResetToken.findUnique({
      where: { token },
      include: { user: true },
    });

    if (!resetToken || resetToken.usedAt || resetToken.expiresAt < new Date()) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Validate new password
    const passwordStrength =
      this.cryptoService.validatePasswordStrength(newPassword);
    if (!passwordStrength.isStrong) {
      throw new BadRequestException({
        message: 'Password does not meet requirements',
        feedback: passwordStrength.feedback,
      });
    }

    // Hash new password
    const hashedPassword =
      await this.cryptoService.hashPasswordArgon2(newPassword);

    // Update user password
    await this.prisma.user.update({
      where: { id: resetToken.user.id },
      data: { password: hashedPassword },
    });

    // Mark token as used
    await this.prisma.passwordResetToken.update({
      where: { id: resetToken.id },
      data: { usedAt: new Date() },
    });

    // Terminate all user sessions for security
    await this.sessionService.terminateAllUserSessions(
      resetToken.user.id.toString(),
      undefined,
      'Password reset',
    );

    // Revoke all refresh tokens
    await this.tokenService.revokeAllUserTokens(
      resetToken.user.id.toString(),
      'Password reset',
    );

    this.logger.log(`Password reset completed for user ${resetToken.user.id}`);
  }

  async verifyEmail(token: string, ipAddress: string): Promise<void> {
    const verificationToken =
      await this.prisma.emailVerificationToken.findUnique({
        where: { token },
        include: { user: true },
      });

    if (
      !verificationToken ||
      verificationToken.expiresAt < new Date()
    ) {
      throw new BadRequestException('Invalid or expired verification token');
    }

    // Mark user as verified
    await this.prisma.user.update({
      where: { id: verificationToken.user.id },
      data: { isEmailVerified: true },
    });

    // Delete the used token
    await this.prisma.emailVerificationToken.delete({
      where: { id: verificationToken.id },
    });

    this.logger.log(`Email verified for user ${verificationToken.user.id}`);
  }

  async sendEmailVerification(user: User, ipAddress: string): Promise<void> {
    const tokenValue = this.cryptoService.generateSecureToken(32);
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiry

    const verificationToken = await this.prisma.emailVerificationToken.create({
      data: {
        token: tokenValue,
        userId: user.id,
        expiresAt,
      },
    });

    // TODO: Send verification email
    this.logger.log(`Email verification token generated for user ${user.id.toString()}`);
  }

  async validateUser(userId: number): Promise<User | null> {
    return this.prisma.user.findFirst({
      where: {
        id: userId,
        isActive: true,
      },
      include: {
        roles: {
          include: {
            role: true,
          },
        },
        departments: {
          include: {
            department: true,
          },
        },
      },
    });
  }

  async changePassword(
    userId: number,
    currentPassword: string,
    newPassword: string,
  ): Promise<void> {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await this.cryptoService.verifyPassword(
      currentPassword,
      user.password,
    );
    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Validate new password
    const passwordStrength =
      this.cryptoService.validatePasswordStrength(newPassword);
    if (!passwordStrength.isStrong) {
      throw new BadRequestException({
        message: 'Password does not meet requirements',
        feedback: passwordStrength.feedback,
      });
    }

    // Hash new password
    const hashedPassword =
      await this.cryptoService.hashPasswordArgon2(newPassword);
    await this.prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    // Terminate other sessions for security
    await this.sessionService.terminateAllUserSessions(
      userId.toString(),
      undefined,
      'Password change',
    );

    this.logger.log(`Password changed for user ${userId}`);
  }

  private async recordFailedAttempt(
    email: string,
    deviceInfo?: { userAgent?: string; ipAddress?: string },
    reason?: string,
    userId?: number,
  ): Promise<void> {
    await this.securityService.recordAuthAttempt(
      AuthAttemptType.LOGIN,
      AuthAttemptResult.FAILURE,
      email,
      deviceInfo?.ipAddress || '',
      deviceInfo?.userAgent,
      reason,
      userId?.toString(),
    );
  }
}
