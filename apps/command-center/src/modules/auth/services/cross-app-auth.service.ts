import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { TokenService, JwtPayload } from './token.service';
import { User } from '../../../common/types/prisma.types';

export interface CrossAppTokenPayload extends JwtPayload {
  // Cross-app specific fields
  appId: string;
  allowedApps: string[];
  crossAppPermissions: string[];
  issuedFor: string;
  originApp: string;
}

export interface CrossAppConfig {
  appId: string;
  appSecret: string;
  allowedOrigins: string[];
  tokenExpiryMinutes: number;
}

export interface AppTokenRequest {
  targetAppId: string;
  permissions: string[];
  sessionData?: Record<string, any>;
}

@Injectable()
export class CrossAppAuthService {
  private readonly logger = new Logger(CrossAppAuthService.name);
  private readonly crossAppSecret: string;
  private readonly allowedApps: Map<string, CrossAppConfig> = new Map();

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly tokenService: TokenService,
  ) {
    this.crossAppSecret = this.configService.get<string>(
      'CROSS_APP_JWT_SECRET',
      this.configService.get<string>('JWT_SECRET'),
    );

    // Initialize allowed apps from configuration
    this.initializeAllowedApps();
  }

  private initializeAllowedApps(): void {
    // Command Center (Backend API)
    this.allowedApps.set('command-center', {
      appId: 'command-center',
      appSecret: this.configService.get('CC_APP_SECRET', 'cc_secret_key'),
      allowedOrigins: [
        'http://localhost:3000',
        'https://command-center.luminar.app',
      ],
      tokenExpiryMinutes: 30,
    });

    // Learning Dashboard
    this.allowedApps.set('learning-dashboard', {
      appId: 'learning-dashboard',
      appSecret: this.configService.get('LD_APP_SECRET', 'ld_secret_key'),
      allowedOrigins: ['http://localhost:3001', 'https://learn.luminar.app'],
      tokenExpiryMinutes: 60,
    });

    // Skills Assessment
    this.allowedApps.set('skills-assessment', {
      appId: 'skills-assessment',
      appSecret: this.configService.get('SA_APP_SECRET', 'sa_secret_key'),
      allowedOrigins: ['http://localhost:3002', 'https://assess.luminar.app'],
      tokenExpiryMinutes: 45,
    });

    // Talent Analytics
    this.allowedApps.set('talent-analytics', {
      appId: 'talent-analytics',
      appSecret: this.configService.get('TA_APP_SECRET', 'ta_secret_key'),
      allowedOrigins: [
        'http://localhost:3003',
        'https://analytics.luminar.app',
      ],
      tokenExpiryMinutes: 30,
    });

    // Admin Portal
    this.allowedApps.set('admin-portal', {
      appId: 'admin-portal',
      appSecret: this.configService.get('AP_APP_SECRET', 'ap_secret_key'),
      allowedOrigins: ['http://localhost:3004', 'https://admin.luminar.app'],
      tokenExpiryMinutes: 15,
    });

    this.logger.log(
      `Initialized ${this.allowedApps.size} cross-app configurations`,
    );
  }

  /**
   * Generate a cross-app JWT token for the user
   */
  async generateCrossAppToken(
    user: User,
    request: AppTokenRequest,
    originApp: string = 'command-center',
  ): Promise<string> {
    const targetApp = this.allowedApps.get(request.targetAppId);
    if (!targetApp) {
      throw new Error(`Target app ${request.targetAppId} is not configured`);
    }

    // Create cross-app payload
    const payload: CrossAppTokenPayload = {
      // Standard JWT fields
      sub: user.id.toString(),
      email: user.email,
      roles: user.role ? [user.role] : [],
      permissions: [],
      sessionId: this.generateSessionId(),
      jti: this.generateJti(),

      // Cross-app specific fields
      appId: request.targetAppId,
      allowedApps: [originApp, request.targetAppId],
      crossAppPermissions: request.permissions,
      issuedFor: request.targetAppId,
      originApp,
    };

    // Sign with cross-app secret
    const token = await this.jwtService.signAsync(payload, {
      secret: this.crossAppSecret,
      expiresIn: `${targetApp.tokenExpiryMinutes}m`,
      issuer: 'luminar-sso',
      audience: request.targetAppId,
    });

    this.logger.log(
      `Generated cross-app token for user ${user.id} to access ${request.targetAppId}`,
    );

    return token;
  }

  /**
   * Verify a cross-app JWT token
   */
  async verifyCrossAppToken(
    token: string,
    appId: string,
  ): Promise<CrossAppTokenPayload> {
    try {
      const payload = await this.jwtService.verifyAsync<CrossAppTokenPayload>(
        token,
        {
          secret: this.crossAppSecret,
          issuer: 'luminar-sso',
          audience: appId,
        },
      );

      // Verify app is allowed
      if (!payload.allowedApps.includes(appId)) {
        throw new Error(`Token not valid for app ${appId}`);
      }

      // Verify target app matches
      if (payload.issuedFor !== appId) {
        throw new Error(
          `Token was issued for ${payload.issuedFor}, not ${appId}`,
        );
      }

      return payload;
    } catch (error) {
      this.logger.error(
        `Cross-app token verification failed: ${error.message}`,
      );
      throw new Error('Invalid cross-app token');
    }
  }

  /**
   * Exchange a regular JWT for a cross-app token
   */
  async exchangeTokenForCrossApp(
    originalToken: string,
    request: AppTokenRequest,
  ): Promise<string> {
    // Verify the original token
    const originalPayload =
      await this.tokenService.verifyAccessToken(originalToken);

    // Get user info (in production, fetch from database)
    const user = {
      id: parseInt(originalPayload.sub),
      email: originalPayload.email,
      password: '',
      name: originalPayload.email.split('@')[0],
      firstName: null,
      lastName: null,
      role: originalPayload.roles?.[0] || 'user',
      isActive: true,
      isEmailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: new Date(),
      refreshToken: null,
    } as User;

    return this.generateCrossAppToken(user, request, 'command-center');
  }

  /**
   * Validate app credentials for cross-app requests
   */
  validateAppCredentials(appId: string, appSecret: string): boolean {
    const appConfig = this.allowedApps.get(appId);
    return appConfig && appConfig.appSecret === appSecret;
  }

  /**
   * Get app configuration
   */
  getAppConfig(appId: string): CrossAppConfig | undefined {
    return this.allowedApps.get(appId);
  }

  /**
   * Check if origin is allowed for app
   */
  isOriginAllowedForApp(appId: string, origin: string): boolean {
    const appConfig = this.allowedApps.get(appId);
    if (!appConfig) return false;

    return appConfig.allowedOrigins.some((allowedOrigin) => {
      // Support wildcard matching for development
      if (allowedOrigin.includes('localhost')) {
        return origin.includes('localhost');
      }
      return origin === allowedOrigin;
    });
  }

  /**
   * Generate logout tokens for all apps
   */
  async generateLogoutTokens(user: User): Promise<Record<string, string>> {
    const logoutTokens: Record<string, string> = {};

    for (const [appId, appConfig] of this.allowedApps) {
      if (appId === 'command-center') continue; // Skip self

      const logoutPayload = {
        sub: user.id,
        type: 'logout',
        appId,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 300, // 5 minutes
      };

      logoutTokens[appId] = await this.jwtService.signAsync(logoutPayload, {
        secret: this.crossAppSecret,
        issuer: 'luminar-sso',
        audience: appId,
      });
    }

    return logoutTokens;
  }

  /**
   * Verify logout token
   */
  async verifyLogoutToken(
    token: string,
    appId: string,
  ): Promise<{ userId: string }> {
    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.crossAppSecret,
        issuer: 'luminar-sso',
        audience: appId,
      });

      if (payload.type !== 'logout') {
        throw new Error('Invalid token type');
      }

      return { userId: payload.sub };
    } catch (error) {
      throw new Error('Invalid logout token');
    }
  }

  /**
   * Get user permissions for specific app
   */
  getUserPermissionsForApp(user: User, appId: string): string[] {
    // Define app-specific permission mappings
    const appPermissionMappings: Record<string, Record<string, string[]>> = {
      'learning-dashboard': {
        admin: ['manage_courses', 'view_analytics', 'manage_users'],
        instructor: ['create_courses', 'view_student_progress'],
        student: ['view_courses', 'take_assessments'],
        manager: ['view_team_progress', 'assign_learning'],
      },
      'skills-assessment': {
        admin: ['create_assessments', 'view_all_results'],
        instructor: ['create_assessments', 'view_results'],
        student: ['take_assessments', 'view_own_results'],
        manager: ['view_team_results', 'assign_assessments'],
      },
      'talent-analytics': {
        admin: ['view_all_analytics', 'export_data'],
        manager: ['view_team_analytics'],
        hr: ['view_org_analytics', 'generate_reports'],
      },
      'admin-portal': {
        admin: ['full_access'],
        hr: ['user_management', 'reporting'],
      },
    };

    const appMappings = appPermissionMappings[appId] || {};
    const userPermissions: Set<string> = new Set();

    // Add permissions based on user role
    if (user.role) {
      const rolePermissions = appMappings[user.role] || [];
      rolePermissions.forEach((permission) => userPermissions.add(permission));
    }

    return Array.from(userPermissions);
  }

  private generateSessionId(): string {
    return `cross_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateJti(): string {
    return `jti_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Refresh cross-app token
   */
  async refreshCrossAppToken(
    expiredToken: string,
    appId: string,
  ): Promise<string> {
    try {
      // Verify expired token (ignore expiration for payload extraction)
      const payload = this.jwtService.decode(
        expiredToken,
      ) as CrossAppTokenPayload;

      if (!payload || payload.issuedFor !== appId) {
        throw new Error('Invalid token for refresh');
      }

      // Create new token with same permissions
      const newPayload: CrossAppTokenPayload = {
        ...payload,
        jti: this.generateJti(),
        sessionId: this.generateSessionId(),
      };

      const targetApp = this.allowedApps.get(appId);
      if (!targetApp) {
        throw new Error(`App ${appId} not configured`);
      }

      return await this.jwtService.signAsync(newPayload, {
        secret: this.crossAppSecret,
        expiresIn: `${targetApp.tokenExpiryMinutes}m`,
        issuer: 'luminar-sso',
        audience: appId,
      });
    } catch (error) {
      this.logger.error(`Failed to refresh cross-app token: ${error.message}`);
      throw new Error('Token refresh failed');
    }
  }
}
