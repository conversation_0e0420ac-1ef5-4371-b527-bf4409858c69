import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../database/prisma/prisma.service';
import {
  AuthAttempt,
  AuthAttemptType,
  AuthAttemptResult,
} from '../../../common/types/prisma.types';
import { CryptoService } from './crypto.service';

export interface SecurityCheck {
  isAllowed: boolean;
  riskScore: number;
  blockedReason?: string;
  requiresAdditionalVerification?: boolean;
}

export interface RateLimitConfig {
  maxAttempts: number;
  windowMinutes: number;
  blockDurationMinutes: number;
}

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);

  // Rate limiting configurations
  private readonly rateLimits: Record<string, RateLimitConfig> = {
    login: { maxAttempts: 5, windowMinutes: 15, blockDurationMinutes: 30 },
    password_reset: {
      maxAttempts: 3,
      windowMinutes: 60,
      blockDurationMinutes: 60,
    },
    email_verification: {
      maxAttempts: 5,
      windowMinutes: 60,
      blockDurationMinutes: 60,
    },
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly cryptoService: CryptoService,
    private readonly prisma: PrismaService,
  ) {}

  async checkSecurityPolicy(
    type: AuthAttemptType,
    identifier: string, // email or user ID
    ipAddress: string,
    userAgent?: string,
    additionalData?: any,
  ): Promise<SecurityCheck> {
    const riskScore = await this.calculateRiskScore(
      identifier,
      ipAddress,
      userAgent,
      additionalData,
    );

    // Check rate limiting
    const rateLimitResult = await this.checkRateLimit(
      type,
      identifier,
      ipAddress,
    );
    if (!rateLimitResult.isAllowed) {
      return {
        isAllowed: false,
        riskScore,
        blockedReason: 'Rate limit exceeded',
      };
    }

    // Check IP-based blocking
    const ipBlocked = await this.isIpBlocked(ipAddress);
    if (ipBlocked) {
      return {
        isAllowed: false,
        riskScore,
        blockedReason: 'IP address blocked',
      };
    }

    // Check geolocation anomalies
    const geoAnomalyDetected = await this.detectGeolocationAnomaly(
      identifier,
      ipAddress,
    );

    // Determine if additional verification is required
    const requiresAdditionalVerification =
      riskScore > 70 ||
      geoAnomalyDetected ||
      (await this.isNewDevice(identifier, userAgent));

    return {
      isAllowed: riskScore < 90, // Block if risk score is too high
      riskScore,
      requiresAdditionalVerification,
    };
  }

  async recordAuthAttempt(
    type: AuthAttemptType,
    result: AuthAttemptResult,
    identifier: string,
    ipAddress: string,
    userAgent?: string,
    failureReason?: string,
    userId?: string,
  ): Promise<void> {
    const riskScore = await this.calculateRiskScore(
      identifier,
      ipAddress,
      userAgent,
    );

    // Create metadata object to store additional fields not directly supported by Prisma model
    const metadata = {
      deviceFingerprint: this.cryptoService.generateDeviceFingerprint(
        userAgent || '',
        ipAddress,
      ),
      failureReason,
      riskScore,
      isSuspicious: riskScore > 70,
      isBlocked: result === AuthAttemptResult.BLOCKED,
    };

    await this.prisma.authAttempt.create({
      data: {
        type,
        result,
        email: identifier.includes('@') ? identifier : null,
        userId,
        ipAddress,
        userAgent,
        details: metadata,
      },
    });

    // Auto-block suspicious activity
    if (riskScore > 85 || result === AuthAttemptResult.BLOCKED) {
      await this.autoBlockSuspiciousActivity(identifier, ipAddress);
    }
  }

  private async checkRateLimit(
    type: AuthAttemptType,
    identifier: string,
    ipAddress: string,
  ): Promise<{ isAllowed: boolean; remainingAttempts: number }> {
    const config = this.rateLimits[type] || this.rateLimits.login;
    const windowStart = new Date();
    windowStart.setMinutes(windowStart.getMinutes() - config.windowMinutes);

    // Check attempts by email/user
    const userAttempts = await this.prisma.authAttempt.count({
      where: {
        type,
        ...(identifier.includes('@')
          ? { email: identifier }
          : { userId: identifier }),
        createdAt: { gte: windowStart },
        result: AuthAttemptResult.FAILURE,
      },
    });

    // Check attempts by IP
    const ipAttempts = await this.prisma.authAttempt.count({
      where: {
        type,
        ipAddress,
        createdAt: { gte: windowStart },
        result: AuthAttemptResult.FAILURE,
      },
    });

    const maxAttempts = Math.max(userAttempts, ipAttempts);
    const isAllowed = maxAttempts < config.maxAttempts;
    const remainingAttempts = Math.max(0, config.maxAttempts - maxAttempts);

    return { isAllowed, remainingAttempts };
  }

  private async calculateRiskScore(
    identifier: string,
    ipAddress: string,
    userAgent?: string,
    additionalData?: any,
  ): Promise<number> {
    let riskScore = 0;

    // Base risk factors
    riskScore += await this.getIpRisk(ipAddress);
    riskScore += await this.getUserAgentRisk(userAgent);
    riskScore += await this.getTimeBasedRisk();
    riskScore += await this.getFrequencyRisk(identifier, ipAddress);

    // Geolocation risk
    riskScore += await this.getGeolocationRisk(identifier, ipAddress);

    // Device fingerprint risk
    if (userAgent) {
      riskScore += await this.getDeviceRisk(identifier, userAgent, ipAddress);
    }

    return Math.min(100, riskScore);
  }

  private async getIpRisk(ipAddress: string): Promise<number> {
    // Check if IP is from known threat sources
    // In production, integrate with threat intelligence feeds
    const suspiciousIps = this.configService
      .get('SUSPICIOUS_IPS', '')
      .split(',');
    if (suspiciousIps.includes(ipAddress)) {
      return 50;
    }

    // Check recent failed attempts from this IP
    const recentFailures = await this.prisma.authAttempt.count({
      where: {
        ipAddress,
        result: AuthAttemptResult.FAILURE,
        createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
      },
    });

    return Math.min(30, recentFailures * 5);
  }

  private async getUserAgentRisk(userAgent?: string): Promise<number> {
    if (!userAgent) return 10;

    // Check for suspicious user agents
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /curl/i,
      /wget/i,
      /python/i,
      /automated/i,
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(userAgent)) {
        return 25;
      }
    }

    return 0;
  }

  private async getTimeBasedRisk(): Promise<number> {
    const hour = new Date().getHours();

    // Higher risk during unusual hours (late night/early morning)
    if (hour >= 2 && hour <= 6) {
      return 15;
    }

    return 0;
  }

  private async getFrequencyRisk(
    identifier: string,
    ipAddress: string,
  ): Promise<number> {
    const lastHour = new Date();
    lastHour.setHours(lastHour.getHours() - 1);

    // Count attempts by email/identifier
    const emailAttempts = await this.prisma.authAttempt.count({
      where: {
        email: identifier,
        createdAt: { gte: lastHour },
      },
    });

    // Count attempts by IP
    const ipAttempts = await this.prisma.authAttempt.count({
      where: {
        ipAddress,
        createdAt: { gte: lastHour },
      },
    });

    const totalAttempts = Math.max(emailAttempts, ipAttempts);

    // Higher frequency = higher risk
    return Math.min(25, totalAttempts * 3);
  }

  private async getGeolocationRisk(
    identifier: string,
    ipAddress: string,
  ): Promise<number> {
    // In production, integrate with geolocation service
    // Check if login location is unusual for this user

    const lastSuccessfulAttempt = await this.prisma.authAttempt.findFirst({
      where: {
        email: identifier,
        result: AuthAttemptResult.SUCCESS,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!lastSuccessfulAttempt) {
      return 0; // New user, no historical data
    }

    // Simplified geolocation check
    // In production, calculate actual distance between IPs
    if (lastSuccessfulAttempt.ipAddress !== ipAddress) {
      return 20; // Different IP location
    }

    return 0;
  }

  private async getDeviceRisk(
    identifier: string,
    userAgent: string,
    ipAddress: string,
  ): Promise<number> {
    const deviceFingerprint = this.cryptoService.generateDeviceFingerprint(
      userAgent,
      ipAddress,
    );

    // Check if device fingerprint exists in metadata of successful attempts
    const knownDevice = await this.prisma.authAttempt.findFirst({
      where: {
        email: identifier,
        result: AuthAttemptResult.SUCCESS,
        metadata: {
          path: ['deviceFingerprint'],
          equals: deviceFingerprint,
        },
      },
    });

    return knownDevice ? 0 : 15; // New device
  }

  private async isIpBlocked(ipAddress: string): Promise<boolean> {
    const blockThreshold = 10; // Number of recent failures to trigger block
    const timeWindow = new Date();
    timeWindow.setHours(timeWindow.getHours() - 24);

    const failures = await this.prisma.authAttempt.count({
      where: {
        ipAddress,
        result: AuthAttemptResult.FAILURE,
        createdAt: { gte: timeWindow },
      },
    });

    return failures >= blockThreshold;
  }

  private async detectGeolocationAnomaly(
    identifier: string,
    ipAddress: string,
  ): Promise<boolean> {
    // Simple implementation - in production, use proper geolocation service
    const recentSuccessfulAttempts = await this.prisma.authAttempt.findMany({
      where: {
        email: identifier,
        result: AuthAttemptResult.SUCCESS,
        createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
    });

    if (recentSuccessfulAttempts.length === 0) {
      return false;
    }

    // Check if current IP is significantly different from recent ones
    const recentIps = recentSuccessfulAttempts.map(
      (attempt) => attempt.ipAddress,
    );
    return !recentIps.includes(ipAddress);
  }

  private async isNewDevice(
    identifier: string,
    userAgent?: string,
  ): Promise<boolean> {
    if (!userAgent) return true;

    const existingDevice = await this.prisma.authAttempt.findFirst({
      where: {
        email: identifier,
        userAgent,
        result: AuthAttemptResult.SUCCESS,
      },
    });

    return !existingDevice;
  }

  private async autoBlockSuspiciousActivity(
    identifier: string,
    ipAddress: string,
  ): Promise<void> {
    this.logger.warn(
      `Auto-blocking suspicious activity for ${identifier} from ${ipAddress}`,
    );

    // Record blocking attempt
    await this.recordAuthAttempt(
      AuthAttemptType.LOGIN,
      AuthAttemptResult.BLOCKED,
      identifier,
      ipAddress,
      undefined,
      'Auto-blocked due to suspicious activity',
    );

    // In production, implement actual blocking mechanism
    // This could involve updating a blocklist table or cache
  }

  // Clean up old auth attempts
  async cleanupOldAttempts(): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 90); // Keep 90 days

    const result = await this.prisma.authAttempt.deleteMany({
      where: {
        createdAt: { lt: cutoffDate },
      },
    });

    this.logger.log(`Cleaned up ${result.count} old auth attempts`);
    return result.count;
  }

  // Security metrics
  async getSecurityMetrics(days: number = 7): Promise<{
    totalAttempts: number;
    successfulAttempts: number;
    failedAttempts: number;
    blockedAttempts: number;
    suspiciousAttempts: number;
    topFailureReasons: Array<{ reason: string; count: number }>;
    topSuspiciousIps: Array<{ ip: string; count: number }>;
  }> {
    const since = new Date();
    since.setDate(since.getDate() - days);

    const [
      totalAttempts,
      successfulAttempts,
      failedAttemptsCount,
      blockedAttempts,
      suspiciousAttempts,
    ] = await Promise.all([
      this.prisma.authAttempt.count({
        where: { createdAt: { gte: since } },
      }),
      this.prisma.authAttempt.count({
        where: {
          createdAt: { gte: since },
          result: AuthAttemptResult.SUCCESS,
        },
      }),
      this.prisma.authAttempt.count({
        where: {
          createdAt: { gte: since },
          result: AuthAttemptResult.FAILURE,
        },
      }),
      this.prisma.authAttempt.count({
        where: {
          createdAt: { gte: since },
          result: AuthAttemptResult.BLOCKED,
        },
      }),
      // Count suspicious attempts by checking metadata field
      this.prisma.authAttempt.count({
        where: {
          createdAt: { gte: since },
          metadata: {
            path: ['isSuspicious'],
            equals: true,
          },
        },
      }),
    ]);

    // Get top failure reasons by fetching raw data and processing in memory
    const failedAttemptsData = await this.prisma.authAttempt.findMany({
      where: {
        createdAt: { gte: since },
        result: AuthAttemptResult.FAILURE,
        metadata: {
          path: ['failureReason'],
          not: null,
        },
      },
      select: {
        metadata: true,
      },
    });

    // Simplified approach - just get all suspicious attempts and group by IP in memory
    const suspiciousAttemptsData = await this.prisma.authAttempt.findMany({
      where: {
        createdAt: { gte: since },
        metadata: {
          path: ['isSuspicious'],
          equals: true,
        },
        ipAddress: {
          not: null,
        },
      },
      select: {
        ipAddress: true,
      },
      take: 100, // Limit to avoid memory issues
    });

    // Process failure reasons in memory
    const failureReasonCounts: Record<string, number> = {};
    failedAttemptsData.forEach((attempt) => {
      const failureReason = (attempt.metadata as any)?.failureReason;
      if (failureReason) {
        failureReasonCounts[failureReason] =
          (failureReasonCounts[failureReason] || 0) + 1;
      }
    });

    // Process suspicious IPs in memory
    const ipCounts: Record<string, number> = {};
    suspiciousAttemptsData.forEach((attempt) => {
      if (attempt.ipAddress) {
        ipCounts[attempt.ipAddress] = (ipCounts[attempt.ipAddress] || 0) + 1;
      }
    });

    const topFailureReasons = Object.entries(failureReasonCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([reason, count]) => ({ reason, count }));

    const topSuspiciousIps = Object.entries(ipCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([ip, count]) => ({ ip, count }));

    return {
      totalAttempts,
      successfulAttempts,
      failedAttempts: failedAttemptsCount,
      blockedAttempts,
      suspiciousAttempts,
      topFailureReasons,
      topSuspiciousIps,
    };
  }

  async getSecurityStatistics(): Promise<any> {
    try {
      const metrics = await this.getSecurityMetrics();
      return {
        ...metrics,
        timestamp: new Date(),
        summary: 'Security statistics generated successfully',
      };
    } catch (error) {
      this.logger.error('Failed to get security statistics:', error);
      throw new Error('Failed to retrieve security statistics');
    }
  }

  async getAuthAttempts(page: number = 1, limit: number = 10): Promise<any> {
    try {
      const skip = (page - 1) * limit;

      const [attempts, total] = await Promise.all([
        this.prisma.authAttempt.findMany({
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
        }),
        this.prisma.authAttempt.count(),
      ]);

      return {
        attempts,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error('Failed to get auth attempts:', error);
      throw new Error('Failed to retrieve authentication attempts');
    }
  }
}
