import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import {
  RoleService,
  CreateRoleDto,
  UpdateRoleDto,
  CreatePermissionDto,
} from './services/role.service';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import { Roles } from './decorators/roles.decorator';
import { RequirePermissions } from './decorators/permissions.decorator';
import { SanitizationPipe } from '../../common/validators/sanitization.pipe';

export class AssignRoleDto {
  userId: string;
  roleName: string;
}

export class RemoveRoleDto {
  userId: string;
  roleName: string;
}

export class RolePermissionsDto {
  permissions: string[];
}

@ApiTags('Admin - Role Management')
@Controller('admin')
@UseGuards(AuthGuard('jwt'), RolesGuard, PermissionsGuard)
@ApiBearerAuth()
export class AdminController {
  constructor(private readonly roleService: RoleService) {}

  // Role Management
  @Post('roles')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Role created successfully' })
  @ApiResponse({ status: 400, description: 'Role already exists' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async createRole(@Body(new SanitizationPipe()) body: CreateRoleDto) {
    const role = await this.roleService.createRole(body);
    return {
      message: 'Role created successfully',
      role,
    };
  }

  @Get('roles')
  @Roles('admin', 'developer', 'moderator')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Get all roles' })
  @ApiResponse({ status: 200, description: 'Roles retrieved successfully' })
  async getAllRoles() {
    const roles = await this.roleService.getAllRoles();
    return { roles };
  }

  @Get('roles/:roleId')
  @Roles('admin', 'developer', 'moderator')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiResponse({ status: 200, description: 'Role retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Role not found' })
  async getRoleById(@Param('roleId') roleId: string) {
    const role = await this.roleService.getRoleById(roleId);
    return { role };
  }

  @Put('roles/:roleId')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Update role' })
  @ApiResponse({ status: 200, description: 'Role updated successfully' })
  @ApiResponse({ status: 404, description: 'Role not found' })
  async updateRole(
    @Param('roleId') roleId: string,
    @Body(new SanitizationPipe()) body: UpdateRoleDto,
  ) {
    const role = await this.roleService.updateRole(roleId, body);
    return {
      message: 'Role updated successfully',
      role,
    };
  }

  @Delete('roles/:roleId')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete role' })
  @ApiResponse({ status: 204, description: 'Role deleted successfully' })
  @ApiResponse({
    status: 400,
    description: 'Cannot delete role assigned to users',
  })
  @ApiResponse({ status: 404, description: 'Role not found' })
  async deleteRole(@Param('roleId') roleId: string) {
    await this.roleService.deleteRole(roleId);
  }

  // Permission Management
  @Post('permissions')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new permission' })
  @ApiResponse({ status: 201, description: 'Permission created successfully' })
  @ApiResponse({ status: 400, description: 'Permission already exists' })
  async createPermission(
    @Body(new SanitizationPipe()) body: CreatePermissionDto,
  ) {
    const permission = await this.roleService.createPermission(body);
    return {
      message: 'Permission created successfully',
      permission,
    };
  }

  @Get('permissions')
  @Roles('admin', 'developer', 'moderator')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Get all permissions' })
  @ApiResponse({
    status: 200,
    description: 'Permissions retrieved successfully',
  })
  async getAllPermissions() {
    const permissions = await this.roleService.getAllPermissions();
    return { permissions };
  }

  @Get('permissions/:permissionId')
  @Roles('admin', 'developer', 'moderator')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Get permission by ID' })
  @ApiResponse({
    status: 200,
    description: 'Permission retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'Permission not found' })
  async getPermissionById(@Param('permissionId') permissionId: string) {
    const permission = await this.roleService.getPermissionById(permissionId);
    return { permission };
  }

  @Put('permissions/:permissionId')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Update permission' })
  @ApiResponse({ status: 200, description: 'Permission updated successfully' })
  @ApiResponse({ status: 404, description: 'Permission not found' })
  async updatePermission(
    @Param('permissionId') permissionId: string,
    @Body(new SanitizationPipe()) body: Partial<CreatePermissionDto>,
  ) {
    const permission = await this.roleService.updatePermission(
      permissionId,
      body,
    );
    return {
      message: 'Permission updated successfully',
      permission,
    };
  }

  @Delete('permissions/:permissionId')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete permission' })
  @ApiResponse({ status: 204, description: 'Permission deleted successfully' })
  @ApiResponse({
    status: 400,
    description: 'Cannot delete permission assigned to roles',
  })
  @ApiResponse({ status: 404, description: 'Permission not found' })
  async deletePermission(@Param('permissionId') permissionId: string) {
    await this.roleService.deletePermission(permissionId);
  }

  // Role-Permission Management
  @Post('roles/:roleId/permissions')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Add permissions to role' })
  @ApiResponse({ status: 200, description: 'Permissions added successfully' })
  @ApiResponse({ status: 404, description: 'Role or permissions not found' })
  async addPermissionsToRole(
    @Param('roleId') roleId: string,
    @Body(new SanitizationPipe()) body: RolePermissionsDto,
  ) {
    const role = await this.roleService.addPermissionsToRole(
      roleId,
      body.permissions,
    );
    return {
      message: 'Permissions added to role successfully',
      role,
    };
  }

  @Put('roles/:roleId/permissions')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Set role permissions (replace all)' })
  @ApiResponse({
    status: 200,
    description: 'Role permissions updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Role or permissions not found' })
  async setRolePermissions(
    @Param('roleId') roleId: string,
    @Body(new SanitizationPipe()) body: RolePermissionsDto,
  ) {
    const role = await this.roleService.setRolePermissions(
      roleId,
      body.permissions,
    );
    return {
      message: 'Role permissions updated successfully',
      role,
    };
  }

  @Delete('roles/:roleId/permissions')
  @Roles('admin', 'developer')
  @RequirePermissions('roles:manage')
  @ApiOperation({ summary: 'Remove permissions from role' })
  @ApiResponse({ status: 200, description: 'Permissions removed successfully' })
  @ApiResponse({ status: 404, description: 'Role not found' })
  async removePermissionsFromRole(
    @Param('roleId') roleId: string,
    @Body(new SanitizationPipe()) body: RolePermissionsDto,
  ) {
    const role = await this.roleService.removePermissionsFromRole(
      roleId,
      body.permissions,
    );
    return {
      message: 'Permissions removed from role successfully',
      role,
    };
  }

  // User-Role Management
  @Post('users/assign-role')
  @Roles('admin', 'developer')
  @RequirePermissions('users:write', 'roles:manage')
  @ApiOperation({ summary: 'Assign role to user' })
  @ApiResponse({ status: 200, description: 'Role assigned successfully' })
  @ApiResponse({ status: 404, description: 'User or role not found' })
  @ApiResponse({ status: 400, description: 'User already has this role' })
  async assignRoleToUser(@Body(new SanitizationPipe()) body: AssignRoleDto) {
    const user = await this.roleService.assignRoleToUser(
      body.userId,
      body.roleName,
    );
    return {
      message: `Role '${body.roleName}' assigned to user successfully`,
      user: {
        id: user.id,
        email: user.email,
        role: user.role || '',
      },
    };
  }

  @Post('users/remove-role')
  @Roles('admin', 'developer')
  @RequirePermissions('users:write', 'roles:manage')
  @ApiOperation({ summary: 'Remove role from user' })
  @ApiResponse({ status: 200, description: 'Role removed successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async removeRoleFromUser(@Body(new SanitizationPipe()) body: RemoveRoleDto) {
    const user = await this.roleService.removeRoleFromUser(
      body.userId,
      body.roleName,
    );
    return {
      message: `Role '${body.roleName}' removed from user successfully`,
      user: {
        id: user.id,
        email: user.email,
        role: user.role || '',
      },
    };
  }

  @Get('users/:userId/roles')
  @Roles('admin', 'developer', 'moderator')
  @RequirePermissions('users:read', 'roles:manage')
  @ApiOperation({ summary: 'Get user roles' })
  @ApiResponse({
    status: 200,
    description: 'User roles retrieved successfully',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserRoles(@Param('userId') userId: string) {
    const roles = await this.roleService.getUserRoles(userId);
    return { roles };
  }

  // System Management
  @Post('init-defaults')
  @Roles('admin', 'developer')
  @RequirePermissions('*')
  @ApiOperation({ summary: 'Initialize default roles and permissions' })
  @ApiResponse({
    status: 200,
    description: 'Default roles and permissions initialized',
  })
  async initializeDefaults() {
    await this.roleService.initializeDefaultRoles();
    return {
      message: 'Default roles and permissions initialized successfully',
    };
  }
}
