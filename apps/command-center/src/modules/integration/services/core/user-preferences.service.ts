import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../../database/prisma/prisma.service';
import { UserIntegrationPreferences } from '@prisma/client';
import {
  UserPreferencesDto,
  SyncFrequency,
  IntelligenceLevel,
  PrivacyMode,
} from '../../dto/preferences/user-preferences.dto';
import { EventBusService } from './event-bus.service';
import { CacheManagerService } from './cache-manager.service';

@Injectable()
export class UserPreferencesService {
  private readonly logger = new Logger(UserPreferencesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventBus: EventBusService,
    private readonly cacheManager: CacheManagerService,
  ) {}

  async getUserPreferences(userId: string): Promise<UserPreferencesDto> {
    try {
      // Check cache first
      const cacheKey = `preferences:${userId}`;
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as UserPreferencesDto;
      }

      // Get from database
      let preferences = await this.prisma.userIntegrationPreferences.findFirst({
        where: { userId },
      });

      // Create default preferences if not exists
      if (!preferences) {
        preferences = await this.createDefaultPreferences(userId);
      }

      const dto = this.toDto(preferences);

      // Cache the result
      await this.cacheManager.set(cacheKey, dto, 3600);

      return dto;
    } catch (error) {
      this.logger.error(
        `Failed to get preferences: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async updateUserPreferences(
    userId: string,
    updates: Partial<UserPreferencesDto>,
  ): Promise<UserPreferencesDto> {
    try {
      let preferences = await this.prisma.userIntegrationPreferences.findFirst({
        where: { userId },
      });

      if (!preferences) {
        preferences = await this.createDefaultPreferences(userId);
      }

      // Update preferences
      const updatedPreferences = await this.prisma.userIntegrationPreferences.update({
        where: { id: preferences.id },
        data: {
          ...updates,
          updatedAt: new Date(),
        },
      });

      // Clear cache
      await this.cacheManager.delete(`preferences:${userId}`);

      // Emit update event
      await this.eventBus.emit('preferences.updated', {
        userId,
        preferences: this.toDto(updatedPreferences),
        timestamp: new Date(),
      });

      return this.toDto(updatedPreferences);
    } catch (error) {
      this.logger.error(
        `Failed to update preferences: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async resetToDefaults(userId: string): Promise<UserPreferencesDto> {
    try {
      const defaultPreferences = this.getDefaultPreferences(userId);
      return this.updateUserPreferences(
        userId,
        defaultPreferences as Partial<UserPreferencesDto>,
      );
    } catch (error) {
      this.logger.error(
        `Failed to reset preferences: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getAppIntegrationStatus(userId: string, appName: string): Promise<any> {
    const preferences = await this.getUserPreferences(userId);
    const appKey = `${appName}Integration` as keyof UserPreferencesDto;

    return {
      app: appName,
      enabled: preferences[appKey] || false,
      syncFrequency: preferences.syncFrequency,
      intelligenceLevel: preferences.intelligenceLevel,
      lastUpdated: preferences.updatedAt,
    };
  }

  async toggleAppIntegration(
    userId: string,
    appName: string,
    enabled: boolean,
  ): Promise<UserPreferencesDto> {
    const appKey = `${appName}Integration`;
    const updates = { [appKey]: enabled };

    const updated = await this.updateUserPreferences(userId, updates);

    // Emit app-specific toggle event
    await this.eventBus.emit(`integration.${appName}.toggled`, {
      userId,
      enabled,
      timestamp: new Date(),
    });

    return updated;
  }

  async bulkUpdatePreferences(
    userIds: string[],
    updates: Partial<UserPreferencesDto>,
  ): Promise<void> {
    try {
      await Promise.all(
        userIds.map((userId) => this.updateUserPreferences(userId, updates)),
      );
    } catch (error) {
      this.logger.error(
        `Failed to bulk update preferences: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async createDefaultPreferences(
    userId: string,
  ): Promise<UserIntegrationPreferences> {
    return await this.prisma.userIntegrationPreferences.create({
      data: {
        userId,
        ...this.getDefaultPreferences(userId),
      },
    });
  }

  private getDefaultPreferences(
    userId: string,
  ): Partial<UserIntegrationPreferences> {
    return {
      eConnectIntegration: false,
      lighthouseIntegration: false,
      trainingIntegration: false,
      vendorIntegration: false,
      winsTracking: true,
      realTimeSync: true,
      syncFrequency: SyncFrequency.IMMEDIATE,
      intelligenceLevel: IntelligenceLevel.STANDARD,
      notificationsEnabled: true,
      privacyMode: PrivacyMode.STANDARD,
      dataRetentionDays: 90,
      allowAnonymousAnalytics: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  private toDto(entity: UserIntegrationPreferences): UserPreferencesDto {
    return {
      userId: entity.userId,
      eConnectIntegration: entity.eConnectIntegration,
      lighthouseIntegration: entity.lighthouseIntegration,
      trainingIntegration: entity.trainingIntegration,
      vendorIntegration: entity.vendorIntegration,
      winsTracking: entity.winsTracking,
      realTimeSync: entity.realTimeSync,
      syncFrequency: entity.syncFrequency as SyncFrequency,
      intelligenceLevel: entity.intelligenceLevel as IntelligenceLevel,
      notificationsEnabled: entity.notificationsEnabled,
      privacyMode: entity.privacyMode as PrivacyMode,
      dataRetentionDays: entity.dataRetentionDays,
      allowAnonymousAnalytics: entity.allowAnonymousAnalytics,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }
}
