import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../database/prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { FeatureUsage, FeatureAdoption, FeatureMetrics, UserEvent } from '@prisma/client';

export interface FeatureUsageData {
  featureId: string;
  featureName: string;
  category: string;
  userId: string;
  sessionId: string;
  action: string;
  timestamp: Date;
  duration?: number;
  success: boolean;
  metadata?: Record<string, any>;
}

export interface FeaturePerformanceMetrics {
  featureName: string;
  totalUsage: number;
  uniqueUsers: number;
  averageDuration: number;
  completionRate: number;
  errorRate: number;
  adoptionRate: number;
  retentionRate: number;
  satisfactionScore?: number;
  performanceTrend: Array<{
    date: Date;
    usage: number;
    performance: number;
  }>;
  userSegments: Array<{
    segment: string;
    usage: number;
    performance: number;
  }>;
}

export interface BusinessIntelligenceReport {
  reportType: string;
  generatedAt: Date;
  timeRange: string;
  summary: {
    totalFeatures: number;
    activeFeatures: number;
    totalUsage: number;
    averageAdoptionRate: number;
    topPerformingFeatures: Array<{
      featureId: string;
      featureName: string;
      usage: number;
      adoptionRate: number;
    }>;
    underperformingFeatures: Array<{
      featureId: string;
      featureName: string;
      usage: number;
      adoptionRate: number;
    }>;
  };
  insights: Array<{
    type: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    recommendation: string;
  }>;
  data: Record<string, any>;
}

@Injectable()
export class FeatureAnalyticsService {
  private readonly logger = new Logger(FeatureAnalyticsService.name);
  private usageBuffer: FeatureUsageData[] = [];
  private flushInterval: NodeJS.Timeout;

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.startUsageBufferFlush();
  }

  private startUsageBufferFlush() {
    this.flushInterval = setInterval(() => {
      this.flushUsageBuffer();
    }, 10000); // Flush every 10 seconds
  }

  private async flushUsageBuffer() {
    if (this.usageBuffer.length === 0) return;

    try {
      const usageData = [...this.usageBuffer];
      this.usageBuffer = [];

      await this.prisma.featureUsage.createMany({
        data: usageData,
      });

      // Update adoption tracking
      await this.updateFeatureAdoption(usageData);

      this.logger.debug(`Flushed ${usageData.length} feature usage records`);
    } catch (error) {
      this.logger.error('Error flushing usage buffer:', error);
      // Add back to buffer for retry
      this.usageBuffer.unshift(...this.usageBuffer);
    }
  }

  private async updateFeatureAdoption(usageData: FeatureUsageData[]) {
    const adoptionMap = new Map<string, Set<string>>();

    usageData.forEach((data) => {
      const key = `${data.featureId}:${data.userId}`;
      if (!adoptionMap.has(data.featureId)) {
        adoptionMap.set(data.featureId, new Set());
      }
      adoptionMap.get(data.featureId)!.add(data.userId);
    });

    for (const [featureId, userIds] of adoptionMap) {
      for (const userId of userIds) {
        const existingAdoption = await this.prisma.featureAdoption.findFirst({
          where: { featureId, userId },
        });

        if (!existingAdoption) {
          await this.prisma.featureAdoption.create({
            data: {
              featureId,
              userId,
              firstUsage: new Date(),
              lastUsage: new Date(),
              usageCount: 1,
              isActive: true,
            },
          });
        } else {
          await this.prisma.featureAdoption.update({
            where: { id: existingAdoption.id },
            data: {
              lastUsage: new Date(),
              usageCount: existingAdoption.usageCount + 1,
              isActive: true,
            },
          });
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  private async calculateFeatureMetrics() {
    try {
      const features = await this.getActiveFeatures();

      for (const feature of features) {
        const metrics = await this.calculateFeaturePerformance(
          feature.featureId,
        );

        const existingMetrics = await this.prisma.featureMetrics.findFirst({
          where: {
            featureId: feature.featureId,
            date: new Date(new Date().setHours(0, 0, 0, 0)),
          },
        });

        if (existingMetrics) {
          await this.prisma.featureMetrics.update({
            where: { id: existingMetrics.id },
            data: {
              totalUsage: metrics.totalUsage,
              uniqueUsers: metrics.uniqueUsers,
              successRate: metrics.completionRate,
              averageDuration: metrics.averageDuration,
              errorRate: metrics.errorRate,
              adoptionRate: metrics.adoptionRate,
              retentionRate: metrics.retentionRate,
              updatedAt: new Date(),
            },
          });
        } else {
          await this.prisma.featureMetrics.create({
            data: {
              featureId: feature.featureId,
              featureName: feature.featureName,
              category: feature.category,
              date: new Date(new Date().setHours(0, 0, 0, 0)),
              totalUsage: metrics.totalUsage,
              uniqueUsers: metrics.uniqueUsers,
              successRate: metrics.completionRate,
              averageDuration: metrics.averageDuration,
              errorRate: metrics.errorRate,
              adoptionRate: metrics.adoptionRate,
              retentionRate: metrics.retentionRate,
              metadata: {
                performanceTrend: metrics.performanceTrend,
                userSegments: metrics.userSegments,
              },
            },
          });
        }
      }

      this.logger.log('Feature metrics calculated and saved');
    } catch (error) {
      this.logger.error('Error calculating feature metrics:', error);
    }
  }

  // Public API methods

  public async trackFeatureUsage(usage: FeatureUsageData): Promise<void> {
    this.usageBuffer.push(usage);

    // Emit event for real-time processing
    this.eventEmitter.emit('feature.usage', usage);
  }

  public async getFeatureUsage(filters?: {
    timeRange?: string;
    feature?: string;
    category?: string;
    userId?: string;
  }): Promise<{
    features: FeaturePerformanceMetrics[];
    summary: {
      totalFeatures: number;
      totalUsage: number;
      averageAdoptionRate: number;
    };
  }> {
    const since = filters?.timeRange
      ? this.parseTimeRange(filters.timeRange)
      : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const where: any = {
      timestamp: {
        gte: since,
      },
    };

    if (filters?.feature) {
      where.featureId = filters.feature;
    }

    if (filters?.category) {
      where.category = filters.category;
    }

    if (filters?.userId) {
      where.userId = filters.userId;
    }

    const usageRecords = await this.prisma.featureUsage.findMany({
      where,
    });
    const featureMap = new Map<string, FeatureUsageData[]>();

    usageRecords.forEach((record) => {
      if (!featureMap.has(record.featureId)) {
        featureMap.set(record.featureId, []);
      }
      featureMap.get(record.featureId)!.push(record);
    });

    const features: FeaturePerformanceMetrics[] = [];
    for (const [featureId, records] of featureMap) {
      const metrics = await this.calculateFeaturePerformanceFromRecords(
        featureId,
        records,
      );
      features.push(metrics);
    }

    const totalUsage = usageRecords.length;
    const totalFeatures = features.length;
    const averageAdoptionRate =
      features.reduce((sum, f) => sum + f.adoptionRate, 0) / totalFeatures || 0;

    return {
      features,
      summary: {
        totalFeatures,
        totalUsage,
        averageAdoptionRate,
      },
    };
  }

  public async getAdoptionMetrics(filters?: {
    timeRange?: string;
    feature?: string;
    category?: string;
  }): Promise<{
    overallAdoption: {
      totalUsers: number;
      adoptedUsers: number;
      adoptionRate: number;
    };
    featureAdoption: Array<{
      featureId: string;
      featureName: string;
      totalUsers: number;
      adoptedUsers: number;
      adoptionRate: number;
      averageTimeToAdopt: number;
    }>;
    adoptionTrends: Array<{
      date: string;
      newAdoptions: number;
      cumulativeAdoptions: number;
    }>;
  }> {
    const since = filters?.timeRange
      ? this.parseTimeRange(filters.timeRange)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Get total unique users
    const uniqueUsers = await this.prisma.userEvent.findMany({
      where: {
        timestamp: {
          gte: since,
        },
      },
      distinct: ['userId'],
      select: {
        userId: true,
      },
    });
    const totalUsers = { count: uniqueUsers.length };

    // Get adoption data
    const adoptionWhere: any = {
      firstUsage: {
        gte: since,
      },
    };

    if (filters?.feature) {
      adoptionWhere.featureId = filters.feature;
    }

    const adoptions = await this.prisma.featureAdoption.findMany({
      where: adoptionWhere,
    });
    const adoptedUsers = new Set(adoptions.map((a) => a.userId)).size;

    // Calculate feature-specific adoption
    const featureAdoption = await this.calculateFeatureAdoptionRates(
      since,
      filters?.feature,
      filters?.category,
    );

    // Calculate adoption trends
    const adoptionTrends = await this.calculateAdoptionTrends(
      since,
      filters?.feature,
    );

    return {
      overallAdoption: {
        totalUsers: parseInt(totalUsers.count),
        adoptedUsers,
        adoptionRate: (adoptedUsers / parseInt(totalUsers.count)) * 100,
      },
      featureAdoption,
      adoptionTrends,
    };
  }

  public async getPerformanceMetrics(filters?: {
    feature?: string;
    category?: string;
    timeRange?: string;
  }): Promise<{
    performance: FeaturePerformanceMetrics[];
    benchmarks: {
      averageSuccessRate: number;
      averageDuration: number;
      averageErrorRate: number;
    };
  }> {
    const since = filters?.timeRange
      ? this.parseTimeRange(filters.timeRange)
      : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const where: any = {
      date: {
        gte: since,
      },
    };

    if (filters?.feature) {
      where.featureId = filters.feature;
    }

    if (filters?.category) {
      where.category = filters.category;
    }

    const metricsRecords = await this.prisma.featureMetrics.findMany({
      where,
    });
    const performance: FeaturePerformanceMetrics[] = [];

    for (const record of metricsRecords) {
      const trends = await this.getFeatureTrends(record.featureId, since);
      const performanceTrend = trends.map((trend) => ({
        date: new Date(trend.date),
        usage: trend.usage,
        performance:
          trend.uniqueUsers > 0 ? trend.usage / trend.uniqueUsers : 0, // Calculate performance as usage per unique user
      }));

      performance.push({
        featureName: record.featureName,
        totalUsage: record.totalUsage,
        uniqueUsers: record.uniqueUsers,
        averageDuration: record.averageDuration,
        completionRate: record.successRate || 0,
        errorRate: record.errorRate,
        adoptionRate: record.adoptionRate,
        retentionRate: record.retentionRate,
        performanceTrend,
        userSegments: [],
      });
    }

    const benchmarks = {
      averageSuccessRate:
        performance.reduce((sum, p) => sum + p.completionRate, 0) /
          performance.length || 0,
      averageDuration:
        performance.reduce((sum, p) => sum + p.averageDuration, 0) /
          performance.length || 0,
      averageErrorRate:
        performance.reduce((sum, p) => sum + p.errorRate, 0) /
          performance.length || 0,
    };

    return { performance, benchmarks };
  }

  public async generateBusinessIntelligenceReport(filters?: {
    reportType?: string;
    timeRange?: string;
  }): Promise<BusinessIntelligenceReport> {
    const timeRange = filters?.timeRange || '30d';
    const since = this.parseTimeRange(timeRange);
    const reportType = filters?.reportType || 'comprehensive';

    // Get basic metrics
    const totalFeaturesData = await this.prisma.featureUsage.findMany({
      where: {
        timestamp: {
          gte: since,
        },
      },
      distinct: ['featureId'],
      select: {
        featureId: true,
      },
    });
    const totalFeatures = { count: totalFeaturesData.length };

    const activeFeaturesData = await this.prisma.featureUsage.findMany({
      where: {
        timestamp: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
      },
      distinct: ['featureId'],
      select: {
        featureId: true,
      },
    });
    const activeFeatures = { count: activeFeaturesData.length };

    const totalUsage = await this.prisma.featureUsage.count({
      where: {
        timestamp: {
          gte: since,
        },
      },
    });

    // Get top performing features
    const topPerformingFeatures = await this.getTopPerformingFeatures(
      since,
      10,
    );
    const underperformingFeatures = await this.getUnderperformingFeatures(
      since,
      10,
    );

    const averageAdoptionRate =
      topPerformingFeatures.reduce((sum, f) => sum + f.adoptionRate, 0) /
        topPerformingFeatures.length || 0;

    // Generate insights
    const insights = await this.generateInsights(since, reportType);

    // Collect detailed data based on report type
    const data = await this.collectReportData(reportType, since);

    return {
      reportType,
      generatedAt: new Date(),
      timeRange,
      summary: {
        totalFeatures: parseInt(totalFeatures.count),
        activeFeatures: parseInt(activeFeatures.count),
        totalUsage,
        averageAdoptionRate,
        topPerformingFeatures,
        underperformingFeatures,
      },
      insights,
      data,
    };
  }

  // Private helper methods

  private async getActiveFeatures(): Promise<
    Array<{
      featureId: string;
      featureName: string;
      category: string;
    }>
  > {
    const features = await this.prisma.featureUsage.findMany({
      where: {
        timestamp: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
      distinct: ['featureId'],
      select: {
        featureId: true,
        featureName: true,
        category: true,
      },
    });

    return features;
  }

  private async calculateFeaturePerformance(
    featureId: string,
  ): Promise<FeaturePerformanceMetrics> {
    const since = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const records = await this.prisma.featureUsage.findMany({
      where: {
        featureId,
        timestamp: {
          gte: since,
        },
      },
    });

    return this.calculateFeaturePerformanceFromRecords(featureId, records);
  }

  private async calculateFeaturePerformanceFromRecords(
    featureId: string,
    records: FeatureUsageData[],
  ): Promise<FeaturePerformanceMetrics> {
    const totalUsage = records.length;
    const uniqueUsers = new Set(records.map((r) => r.userId)).size;
    const averageUsagePerUser = uniqueUsers > 0 ? totalUsage / uniqueUsers : 0;
    const successfulUsage = records.filter((r) => r.success).length;
    const successRate =
      totalUsage > 0 ? (successfulUsage / totalUsage) * 100 : 0;
    const errorRate = 100 - successRate;

    const durationsWithValues = records
      .filter((r) => r.duration != null)
      .map((r) => r.duration!);
    const averageDuration =
      durationsWithValues.length > 0
        ? durationsWithValues.reduce((sum, d) => sum + d, 0) /
          durationsWithValues.length
        : 0;

    // Calculate adoption and retention rates
    const adoptionRate = await this.calculateAdoptionRateForFeature(featureId);
    const retentionRate =
      await this.calculateRetentionRateForFeature(featureId);

    const trends = await this.getFeatureTrends(
      featureId,
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    );
    const performanceTrend = trends.map((trend) => ({
      date: new Date(trend.date),
      usage: trend.usage,
      performance: trend.uniqueUsers > 0 ? trend.usage / trend.uniqueUsers : 0, // Calculate performance as usage per unique user
    }));

    return {
      featureName: records[0]?.featureName || 'Unknown',
      totalUsage,
      uniqueUsers,
      averageDuration,
      completionRate: successRate,
      errorRate,
      adoptionRate,
      retentionRate,
      performanceTrend,
      userSegments: [],
    };
  }

  private async calculateAdoptionRateForFeature(
    featureId: string,
  ): Promise<number> {
    const uniqueUsers = await this.prisma.userEvent.findMany({
      distinct: ['userId'],
      select: {
        userId: true,
      },
    });
    const totalUsers = { count: uniqueUsers.length };

    const adoptedUsersList = await this.prisma.featureAdoption.findMany({
      where: { featureId },
      distinct: ['userId'],
      select: {
        userId: true,
      },
    });
    const adoptedUsers = { count: adoptedUsersList.length };

    return totalUsers.count > 0
      ? (adoptedUsers.count / totalUsers.count) * 100
      : 0;
  }

  private async calculateRetentionRateForFeature(
    featureId: string,
  ): Promise<number> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const earlyUsersList = await this.prisma.featureUsage.findMany({
      where: {
        featureId,
        timestamp: {
          gte: thirtyDaysAgo,
          lt: sevenDaysAgo,
        },
      },
      distinct: ['userId'],
      select: {
        userId: true,
      },
    });
    const earlyUsers = { count: earlyUsersList.length };

    // Get the early user IDs for the subquery
    const earlyUserIds = earlyUsersList.map(u => u.userId);

    const returningUsersList = await this.prisma.featureUsage.findMany({
      where: {
        featureId,
        timestamp: {
          gte: sevenDaysAgo,
        },
        userId: {
          in: earlyUserIds,
        },
      },
      distinct: ['userId'],
      select: {
        userId: true,
      },
    });
    const returningUsers = { count: returningUsersList.length };

    return earlyUsers.count > 0
      ? (returningUsers.count / earlyUsers.count) * 100
      : 0;
  }

  private async getFeatureTrends(
    featureId: string,
    since: Date,
  ): Promise<
    Array<{
      date: string;
      usage: number;
      uniqueUsers: number;
    }>
  > {
    const usageData = await this.prisma.featureUsage.findMany({
      where: {
        featureId,
        timestamp: {
          gte: since,
        },
      },
      select: {
        timestamp: true,
        userId: true,
      },
    });

    // Group by date manually
    const trendMap = new Map<string, { usage: number; users: Set<string> }>();
    
    usageData.forEach((record) => {
      const date = record.timestamp.toISOString().split('T')[0];
      
      if (!trendMap.has(date)) {
        trendMap.set(date, { usage: 0, users: new Set() });
      }
      
      const dayData = trendMap.get(date)!;
      dayData.usage++;
      dayData.users.add(record.userId);
    });

    const trends = Array.from(trendMap.entries())
      .map(([date, data]) => ({
        date,
        usage: data.usage,
        uniqueUsers: data.users.size,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return trends;
  }

  private async calculateFeatureAdoptionRates(
    since: Date,
    feature?: string,
    category?: string,
  ): Promise<
    Array<{
      featureId: string;
      featureName: string;
      totalUsers: number;
      adoptedUsers: number;
      adoptionRate: number;
      averageTimeToAdopt: number;
    }>
  > {
    // Implementation would calculate adoption rates for each feature
    // For now, return placeholder
    return [];
  }

  private async calculateAdoptionTrends(
    since: Date,
    feature?: string,
  ): Promise<
    Array<{
      date: string;
      newAdoptions: number;
      cumulativeAdoptions: number;
    }>
  > {
    // Implementation would calculate adoption trends over time
    // For now, return placeholder
    return [];
  }

  private async getTopPerformingFeatures(
    since: Date,
    limit: number,
  ): Promise<
    Array<{
      featureId: string;
      featureName: string;
      usage: number;
      adoptionRate: number;
    }>
  > {
    const features = await this.prisma.featureMetrics.findMany({
      where: {
        date: {
          gte: since,
        },
      },
      orderBy: {
        totalUsage: 'desc',
      },
      take: limit,
    });

    return features.map((f) => ({
      featureId: f.featureId,
      featureName: f.featureName,
      usage: f.totalUsage,
      adoptionRate: f.adoptionRate,
    }));
  }

  private async getUnderperformingFeatures(
    since: Date,
    limit: number,
  ): Promise<
    Array<{
      featureId: string;
      featureName: string;
      usage: number;
      adoptionRate: number;
    }>
  > {
    const features = await this.prisma.featureMetrics.findMany({
      where: {
        date: {
          gte: since,
        },
      },
      orderBy: {
        adoptionRate: 'asc',
      },
      take: limit,
    });

    return features.map((f) => ({
      featureId: f.featureId,
      featureName: f.featureName,
      usage: f.totalUsage,
      adoptionRate: f.adoptionRate,
    }));
  }

  private async generateInsights(
    since: Date,
    reportType: string,
  ): Promise<
    Array<{
      type: string;
      title: string;
      description: string;
      impact: 'high' | 'medium' | 'low';
      recommendation: string;
    }>
  > {
    const insights = [];

    // Analyze feature adoption trends
    const lowAdoptionFeatures = await this.getUnderperformingFeatures(since, 5);
    if (lowAdoptionFeatures.length > 0) {
      insights.push({
        type: 'adoption',
        title: 'Low Feature Adoption Detected',
        description: `${lowAdoptionFeatures.length} features have adoption rates below 10%`,
        impact: 'high' as const,
        recommendation:
          'Consider user onboarding improvements, feature discoverability, or sunset unused features',
      });
    }

    // Analyze usage patterns
    const recentUsage = await this.prisma.featureUsage.count({
      where: {
        timestamp: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
      },
    });

    const previousUsage = await this.prisma.featureUsage.count({
      where: {
        timestamp: {
          gte: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
          lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
      },
    });

    const usageChange =
      previousUsage > 0
        ? ((recentUsage - previousUsage) / previousUsage) * 100
        : 0;

    if (usageChange < -20) {
      insights.push({
        type: 'usage',
        title: 'Significant Usage Decline',
        description: `Overall feature usage has declined by ${Math.abs(usageChange).toFixed(1)}% in the past week`,
        impact: 'high' as const,
        recommendation:
          'Investigate potential issues, user feedback, or competing features',
      });
    } else if (usageChange > 20) {
      insights.push({
        type: 'usage',
        title: 'Strong Usage Growth',
        description: `Overall feature usage has increased by ${usageChange.toFixed(1)}% in the past week`,
        impact: 'medium' as const,
        recommendation: 'Monitor performance and scale resources if needed',
      });
    }

    return insights;
  }

  private async collectReportData(
    reportType: string,
    since: Date,
  ): Promise<Record<string, any>> {
    const data: Record<string, any> = {};

    if (reportType === 'comprehensive' || reportType === 'usage') {
      data.usageByCategory = await this.getUsageByCategory(since);
      data.usageByTimeOfDay = await this.getUsageByTimeOfDay(since);
      data.featureCorrelations = await this.getFeatureCorrelations(since);
    }

    if (reportType === 'comprehensive' || reportType === 'performance') {
      data.performanceMetrics = await this.getPerformanceMetrics({
        timeRange: '30d',
      });
      data.errorAnalysis = await this.getErrorAnalysis(since);
    }

    if (reportType === 'comprehensive' || reportType === 'adoption') {
      data.adoptionFunnels = await this.getAdoptionFunnels(since);
      data.userSegmentation = await this.getUserSegmentation(since);
    }

    return data;
  }

  private async getUsageByCategory(
    since: Date,
  ): Promise<Record<string, number>> {
    const usage = await this.prisma.featureUsage.findMany({
      where: {
        timestamp: {
          gte: since,
        },
      },
      select: {
        category: true,
      },
    });

    // Group and count manually
    const categoryCount = usage.reduce(
      (acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    return categoryCount;
  }

  private async getUsageByTimeOfDay(since: Date): Promise<
    Array<{
      hour: number;
      usage: number;
    }>
  > {
    const usage = await this.prisma.featureUsage.findMany({
      where: {
        timestamp: {
          gte: since,
        },
      },
      select: {
        timestamp: true,
      },
    });

    // Group by hour manually
    const hourlyUsage = new Map<number, number>();
    
    usage.forEach((record) => {
      const hour = record.timestamp.getHours();
      hourlyUsage.set(hour, (hourlyUsage.get(hour) || 0) + 1);
    });

    const result = Array.from(hourlyUsage.entries())
      .map(([hour, count]) => ({ hour, usage: count }))
      .sort((a, b) => a.hour - b.hour);

    return result;
  }

  private async getFeatureCorrelations(since: Date): Promise<
    Array<{
      feature1: string;
      feature2: string;
      correlation: number;
    }>
  > {
    // Implementation would calculate feature usage correlations
    // For now, return placeholder
    return [];
  }

  private async getErrorAnalysis(since: Date): Promise<{
    totalErrors: number;
    errorRate: number;
    topErrors: Array<{
      featureId: string;
      errorCount: number;
      errorRate: number;
    }>;
  }> {
    const totalUsage = await this.prisma.featureUsage.count({
      where: {
        timestamp: {
          gte: since,
        },
      },
    });

    const totalErrors = await this.prisma.featureUsage.count({
      where: {
        timestamp: {
          gte: since,
        },
        success: false,
      },
    });

    const errorRate = totalUsage > 0 ? (totalErrors / totalUsage) * 100 : 0;

    // Get all usage data to calculate errors by feature
    const usageData = await this.prisma.featureUsage.findMany({
      where: {
        timestamp: {
          gte: since,
        },
      },
      select: {
        featureId: true,
        success: true,
      },
    });

    // Group by feature and calculate errors
    const featureStats = new Map<string, { total: number; errors: number }>();
    
    usageData.forEach((record) => {
      if (!featureStats.has(record.featureId)) {
        featureStats.set(record.featureId, { total: 0, errors: 0 });
      }
      
      const stats = featureStats.get(record.featureId)!;
      stats.total++;
      if (!record.success) {
        stats.errors++;
      }
    });

    const topErrors = Array.from(featureStats.entries())
      .filter(([_, stats]) => stats.errors > 0)
      .map(([featureId, stats]) => ({
        featureId,
        errorCount: stats.errors,
        errorRate: (stats.errors / stats.total) * 100,
      }))
      .sort((a, b) => b.errorCount - a.errorCount)
      .slice(0, 10);

    return {
      totalErrors,
      errorRate,
      topErrors,
    };
  }

  private async getAdoptionFunnels(since: Date): Promise<
    Array<{
      funnel: string;
      steps: Array<{
        step: string;
        users: number;
        conversionRate: number;
      }>;
    }>
  > {
    // Implementation would calculate adoption funnels
    // For now, return placeholder
    return [];
  }

  private async getUserSegmentation(since: Date): Promise<{
    segments: Array<{
      segment: string;
      users: number;
      features: Array<{
        featureId: string;
        usage: number;
      }>;
    }>;
  }> {
    // Implementation would calculate user segmentation
    // For now, return placeholder
    return { segments: [] };
  }

  private parseTimeRange(timeRange: string): Date {
    const now = new Date();
    const match = timeRange.match(/(\d+)([hdwm])/);

    if (!match) return new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 'h':
        return new Date(now.getTime() - value * 60 * 60 * 1000);
      case 'd':
        return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
      case 'w':
        return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
      case 'm':
        return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
  }
}
