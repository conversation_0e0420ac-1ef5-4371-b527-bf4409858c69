import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { HttpModule } from '@nestjs/axios';
import { DocumentsService } from './documents.service';
import { DocumentsController } from './documents.controller';
import { RedisModule } from '../redis/redis.module';
import { QueueModule } from '../queue/queue.module';
import { VectorModule } from '../vector/vector.module';

@Module({
  imports: [
    PrismaModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    RedisModule,
    QueueModule,
    VectorModule,
  ],
  controllers: [DocumentsController],
  providers: [DocumentsService],
  exports: [DocumentsService],
})
export class DocumentsModule {}
