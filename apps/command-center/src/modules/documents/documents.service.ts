import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../database/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { RedisService } from '../redis/redis.service';
import { QueueService } from '../queue/queue.service';
import { VectorIntegrationService } from '../vector/services/vector-integration.service';
import { Document, DocumentChunk, Folder, Tag } from '../../common/types/prisma.types';
import { ProcessDocumentDto } from './dto/process-document.dto';
import { ProcessUrlDto } from './dto/process-url.dto';
import { firstValueFrom } from 'rxjs';
import { unlinkSync } from 'fs';
import { join } from 'path';

@Injectable()
export class DocumentsService {
  private readonly logger = new Logger(DocumentsService.name);
  private readonly pythonServiceUrl: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly redisService: RedisService,
    private readonly queueService: QueueService,
    private readonly vectorIntegrationService: VectorIntegrationService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.pythonServiceUrl = this.configService.get<string>(
      'PYTHON_SERVICE_URL',
      'http://localhost:8001',
    );
  }

  async findAllWithFilters(
    ownerId: string,
    page = 1,
    limit = 10,
    filters: {
      folderId?: string;
      tags?: string[];
      documentType?: string[];
      startDate?: string;
      endDate?: string;
    } = {},
  ) {
    const cacheKey = `documents:${ownerId}:${page}:${limit}:${JSON.stringify(filters)}`;

    // Try to get from cache first
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const where: any = {
      ownerId,
      deletedAt: null,
    };

    // Apply filters
    if (filters.folderId) {
      where.folderId = filters.folderId;
    }

    if (filters.tags && filters.tags.length > 0) {
      where.tags = {
        hasEvery: filters.tags,
      };
    }

    if (filters.documentType && filters.documentType.length > 0) {
      where.type = {
        in: filters.documentType,
      };
    }

    if (filters.startDate) {
      where.createdAt = {
        ...where.createdAt,
        gte: new Date(filters.startDate),
      };
    }

    if (filters.endDate) {
      where.createdAt = {
        ...where.createdAt,
        lte: new Date(filters.endDate),
      };
    }

    const [documents, total] = await Promise.all([
      this.prisma.document.findMany({
        where,
        include: {
          owner: true,
          folder: true,
        },
        orderBy: {
          updatedAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.prisma.document.count({ where }),
    ]);

    const result = {
      documents,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };

    // Cache for 5 minutes
    await this.redisService.set(cacheKey, result, 300);
    return result;
  }

  async findAll(ownerId: string, page = 1, limit = 10) {
    return this.findAllWithFilters(ownerId, page, limit);
  }

  async findOne(id: string) {
    const cacheKey = `document:${id}`;

    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      // Update view count asynchronously
      this.incrementViewCount(id);
      return cached;
    }

    const document = await this.prisma.document.findFirst({
      where: { id, deletedAt: null },
      include: {
        owner: true,
        versions: true,
        attachments: true,
        folder: true,
      },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Cache for 15 minutes
    await this.redisService.set(cacheKey, document, 900);

    // Update view count asynchronously
    this.incrementViewCount(id);

    return document;
  }

  async uploadAndProcessDocument(
    file: Express.Multer.File,
    processDto: ProcessDocumentDto,
    ownerId: string,
  ): Promise<{ document: Document; jobId: string }> {
    try {
      // Create document record
      const document = await this.createDocumentFromFile(
        file,
        processDto,
        ownerId,
      );

      // Send file to Python service for processing
      const formData = new FormData();
      const fileBuffer = require('fs').readFileSync(file.path);
      const blob = new Blob([fileBuffer], { type: file.mimetype });
      formData.append('file', blob, file.originalname);
      formData.append(
        'extractMetadata',
        processDto.extractMetadata?.toString() || 'true',
      );
      formData.append('chunkSize', processDto.chunkSize?.toString() || '1000');
      formData.append(
        'chunkOverlap',
        processDto.chunkOverlap?.toString() || '200',
      );

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.pythonServiceUrl}/process/file`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            timeout: 30000, // 30 seconds
          },
        ),
      );

      const { job_id: jobId } = response.data;

      // Store job reference in document metadata
      await this.prisma.document.update({
        where: { id: document.id },
        data: {
          metadata: {
            ...(document.metadata as any || {}),
            processingJobId: jobId,
            processingStatus: 'queued',
          },
        },
      });

      // Queue job to check processing status
      await this.queueService.addDocumentProcessingJob({
        documentId: document.id,
        jobId,
        pythonServiceUrl: this.pythonServiceUrl,
      });

      // Clean up uploaded file
      try {
        unlinkSync(file.path);
      } catch (error) {
        console.warn('Failed to delete uploaded file:', error);
      }

      return { document, jobId };
    } catch (error) {
      // Clean up uploaded file on error
      try {
        unlinkSync(file.path);
      } catch (cleanupError) {
        console.warn(
          'Failed to delete uploaded file after error:',
          cleanupError,
        );
      }
      throw new BadRequestException(
        `Failed to process document: ${error.message}`,
      );
    }
  }

  async uploadAndProcessDocumentsBatch(
    files: Express.Multer.File[],
    processDto: ProcessDocumentDto,
    ownerId: string,
  ): Promise<{ documents: Document[]; jobIds: string[] }> {
    const results = await Promise.allSettled(
      files.map((file) =>
        this.uploadAndProcessDocument(file, processDto, ownerId),
      ),
    );

    const documents: Document[] = [];
    const jobIds: string[] = [];
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        documents.push(result.value.document);
        jobIds.push(result.value.jobId);
      } else {
        errors.push(
          `File ${files[index].originalname}: ${result.reason.message}`,
        );
      }
    });

    if (errors.length > 0 && documents.length === 0) {
      throw new BadRequestException(
        `All files failed to process: ${errors.join(', ')}`,
      );
    }

    return { documents, jobIds };
  }

  async processUrl(
    processDto: ProcessUrlDto,
    ownerId: string,
  ): Promise<{ document: Document; jobId: string }> {
    try {
      // Send URL to Python service for processing
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.pythonServiceUrl}/process/url`,
          processDto,
          {
            timeout: 30000,
          },
        ),
      );

      const { job_id: jobId } = response.data;

      // Create document record
      const document = await this.createDocumentFromUrl(
        processDto,
        ownerId,
        jobId,
      );

      // Queue job to check processing status
      await this.queueService.addDocumentProcessingJob({
        documentId: document.id,
        jobId,
        pythonServiceUrl: this.pythonServiceUrl,
      });

      return { document, jobId };
    } catch (error) {
      throw new BadRequestException(`Failed to process URL: ${error.message}`);
    }
  }

  async getProcessingStatus(jobId: string) {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.pythonServiceUrl}/status/${jobId}`, {
          timeout: 10000,
        }),
      );

      return response.data;
    } catch (error) {
      throw new BadRequestException(
        `Failed to get processing status: ${error.message}`,
      );
    }
  }

  async updateDocumentWithProcessingResult(
    documentId: string,
    processingResult: any,
  ) {
    const document = await this.prisma.document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Update document with processed content
    await this.prisma.document.update({
      where: { id: documentId },
      data: {
        content: processingResult.content,
        metadata: {
          ...(document.metadata as any || {}),
          ...processingResult.metadata,
          processingStatus: 'completed',
          processedAt: new Date().toISOString(),
          wordCount: processingResult.word_count,
          charCount: processingResult.char_count,
          processingTime: processingResult.processing_time,
        },
      },
    });

    // Save chunks
    if (processingResult.chunks && processingResult.chunks.length > 0) {
      await this.saveDocumentChunks(documentId, processingResult.chunks);
    }

    // Index document in vector database
    await this.vectorIntegrationService.indexDocument(document);

    // Invalidate caches
    await this.redisService.del(`document:${documentId}`);
    await this.invalidateUserDocumentsCache(document.ownerId);

    return document;
  }

  async getDocumentChunks(documentId: string, page = 1, limit = 10) {
    const [chunks, total] = await Promise.all([
      this.prisma.documentChunk.findMany({
        where: { documentId },
        orderBy: { chunkIndex: 'asc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.prisma.documentChunk.count({
        where: { documentId },
      }),
    ]);

    return {
      chunks,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getFolders(ownerId: string): Promise<Folder[]> {
    const cacheKey = `folders:${ownerId}`;

    const cached = await this.redisService.get(cacheKey);
    if (cached && Array.isArray(cached)) {
      return cached as Folder[];
    }

    const folders = await this.prisma.folder.findMany({
      where: { ownerId },
      orderBy: { name: 'asc' },
    });

    await this.redisService.set(cacheKey, folders, 300);
    return folders;
  }

  async createFolder(
    createFolderDto: { name: string; parentId?: string },
    ownerId: string,
  ): Promise<Folder> {
    const saved = await this.prisma.folder.create({
      data: {
        ...createFolderDto,
        ownerId,
      },
    });

    // Invalidate cache
    await this.redisService.del(`folders:${ownerId}`);

    return saved;
  }

  async getTags(ownerId: string): Promise<Tag[]> {
    const cacheKey = `tags:${ownerId}`;

    const cached = await this.redisService.get(cacheKey);
    if (cached && Array.isArray(cached)) {
      return cached as Tag[];
    }

    const tags = await this.prisma.tag.findMany({
      where: { ownerId },
      orderBy: { name: 'asc' },
    });

    await this.redisService.set(cacheKey, tags, 300);
    return tags;
  }

  async reprocessDocument(
    documentId: string,
    processDto: ProcessDocumentDto,
  ): Promise<{ jobId: string }> {
    const document = await this.prisma.document.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // If document has a file path, reprocess the file
    if (document.metadata?.filePath) {
      const formData = new FormData();
      const fileBuffer = require('fs').readFileSync(document.metadata.filePath);
      const blob = new Blob([fileBuffer], { type: document.mimeType });
      formData.append('file', blob, document.title);
      formData.append(
        'extractMetadata',
        processDto.extractMetadata?.toString() || 'true',
      );
      formData.append('chunkSize', processDto.chunkSize?.toString() || '1000');
      formData.append(
        'chunkOverlap',
        processDto.chunkOverlap?.toString() || '200',
      );

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.pythonServiceUrl}/process/file`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            timeout: 30000,
          },
        ),
      );

      const { job_id: jobId } = response.data;

      // Update document metadata
      await this.prisma.document.update({
        where: { id: document.id },
        data: {
          metadata: {
            ...(document.metadata as any || {}),
            processingJobId: jobId,
            processingStatus: 'queued',
          },
        },
      });

      return { jobId };
    }

    // If document was from URL, reprocess the URL
    if (document.metadata?.url) {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.pythonServiceUrl}/process/url`,
          {
            url: document.metadata.url,
            extractMetadata: processDto.extractMetadata,
            chunkSize: processDto.chunkSize,
            chunkOverlap: processDto.chunkOverlap,
          },
          {
            timeout: 30000,
          },
        ),
      );

      const { job_id: jobId } = response.data;

      // Update document metadata
      await this.prisma.document.update({
        where: { id: document.id },
        data: {
          metadata: {
            ...(document.metadata as any || {}),
            processingJobId: jobId,
            processingStatus: 'queued',
          },
        },
      });

      return { jobId };
    }

    throw new BadRequestException(
      'Document cannot be reprocessed (no source file or URL)',
    );
  }

  private async createDocumentFromFile(
    file: Express.Multer.File,
    processDto: ProcessDocumentDto,
    ownerId: string,
  ): Promise<Document> {
    const documentType = this.getDocumentTypeFromMimeType(file.mimetype);

    return this.prisma.document.create({
      data: {
        title: processDto.title || file.originalname,
        description: processDto.description,
        type: documentType,
        mimeType: file.mimetype,
        size: file.size,
        ownerId,
        folderId: processDto.folderId || null,
        tags: processDto.tags || [],
        metadata: {
          originalFilename: file.originalname,
          filePath: file.path,
          uploadedAt: new Date().toISOString(),
        },
      },
    });
  }

  private async createDocumentFromUrl(
    processDto: ProcessUrlDto,
    ownerId: string,
    jobId: string,
  ): Promise<Document> {
    const url = new URL(processDto.url);
    const title = processDto.title || `Content from ${url.hostname}`;

    return this.prisma.document.create({
      data: {
        title,
        description: processDto.description,
        type: 'OTHER',
        mimeType: 'text/html',
        ownerId,
        folderId: processDto.folderId || null,
        tags: processDto.tags || [],
        metadata: {
          url: processDto.url,
          domain: url.hostname,
          processingJobId: jobId,
          processingStatus: 'queued',
          crawledAt: new Date().toISOString(),
        },
      },
    });
  }

  private async saveDocumentChunks(documentId: string, chunks: any[]) {
    // Remove existing chunks
    await this.prisma.documentChunk.deleteMany({ where: { documentId } });

    // Save new chunks
    const chunkData = chunks.map((chunk, index) => ({
      id: chunk.id,
      documentId,
      content: chunk.content,
      chunkIndex: index,
      startChar: chunk.start_char,
      endChar: chunk.end_char,
      metadata: chunk.metadata || {},
    }));

    await this.prisma.documentChunk.createMany({
      data: chunkData,
    });
  }

  private getDocumentTypeFromMimeType(mimeType: string): string {
    const typeMapping = {
      'application/pdf': 'PDF',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        'TEXT',
      'application/msword': 'TEXT',
      'text/plain': 'TEXT',
      'text/markdown': 'TEXT',
      'text/html': 'TEXT',
      'image/jpeg': 'IMAGE',
      'image/png': 'IMAGE',
      'image/gif': 'IMAGE',
      'video/mp4': 'VIDEO',
      'audio/mpeg': 'AUDIO',
    };

    return typeMapping[mimeType] || 'OTHER';
  }

  async create(createDocumentDto: any, ownerId: string): Promise<Document> {
    const result = await this.prisma.document.create({
      data: {
        ...createDocumentDto,
        ownerId,
      },
    });

    // Invalidate cache
    await this.invalidateUserDocumentsCache(ownerId);

    // Queue document processing if needed
    if (
      result.type === 'PDF' ||
      result.type === 'IMAGE'
    ) {
      await this.queueService.addFileProcessingJob({
        documentId: result.id,
        operation: 'extract-text',
      });
    }

    // Index document in vector database
    await this.vectorIntegrationService.indexDocument(result);

    return result;
  }

  async update(id: string, updateDocumentDto: any) {
    const document = await this.prisma.document.findFirst({
      where: { id, deletedAt: null },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    const updated = await this.prisma.document.update({
      where: { id },
      data: updateDocumentDto,
    });

    // Invalidate specific document cache
    await this.redisService.del(`document:${id}`);

    // Invalidate user documents cache
    await this.invalidateUserDocumentsCache(document.ownerId);

    // Update document in vector database
    await this.vectorIntegrationService.updateDocumentVector(updated);

    return updated;
  }

  async remove(id: string) {
    const document = await this.prisma.document.findFirst({
      where: { id, deletedAt: null },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    await this.prisma.document.update({
      where: { id },
      data: { deletedAt: new Date() },
    });

    // Remove chunks
    await this.prisma.documentChunk.deleteMany({ where: { documentId: id } });

    // Invalidate caches
    await this.redisService.del(`document:${id}`);
    await this.invalidateUserDocumentsCache(document.ownerId);

    // Remove from vector database
    await this.vectorIntegrationService.removeDocumentVector(id);

    return { message: 'Document deleted successfully' };
  }

  private async incrementViewCount(documentId: string) {
    try {
      await this.prisma.document.update({
        where: { id: documentId },
        data: {
          viewCount: { increment: 1 },
          lastAccessedAt: new Date(),
        },
      });
    } catch (error) {
      // Log error but don't fail the request
      console.error('Failed to increment view count:', error);
    }
  }

  private async invalidateUserDocumentsCache(ownerId: string) {
    // This is a simplified approach - in production you might use pattern matching
    const cacheKeys = [
      `documents:${ownerId}:1:10`,
      `documents:${ownerId}:1:20`,
      `documents:${ownerId}:2:10`,
      // Add more patterns as needed
    ];

    await Promise.all(cacheKeys.map((key) => this.redisService.del(key)));
  }

  async searchDocuments(query: string, ownerId: string) {
    const cacheKey = `search:${ownerId}:${query}`;

    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return cached;
    }

    const documents = await this.prisma.document.findMany({
      where: {
        ownerId,
        deletedAt: null,
        OR: [
          {
            title: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            content: {
              contains: query,
              mode: 'insensitive',
            },
          },
        ],
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: 50,
    });

    // Cache search results for 2 minutes
    await this.redisService.set(cacheKey, documents, 120);

    return documents;
  }

  // Additional method for maintenance processor
  async findPopular(limit: number = 10): Promise<Document[]> {
    return this.prisma.document.findMany({
      where: { deletedAt: null },
      orderBy: { viewCount: 'desc' },
      take: limit,
    });
  }

  async updateDocumentProcessingError(
    documentId: string,
    error: string,
  ): Promise<void> {
    try {
      const document = await this.prisma.document.findUnique({
        where: { id: documentId },
      });

      if (document) {
        await this.prisma.document.update({
          where: { id: documentId },
          data: {
            metadata: {
              ...(document.metadata as any || {}),
              processingStatus: 'failed',
              processingError: error,
              failedAt: new Date().toISOString(),
            },
          },
        });

        // Invalidate caches
        await this.redisService.del(`document:${documentId}`);
        await this.invalidateUserDocumentsCache(document.ownerId);
      }
    } catch (err) {
      this.logger?.error(
        `Failed to update document processing error: ${err.message}`,
      );
    }
  }
}
