import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { DataProcessingLog } from '@prisma/client';

// Define enums that were previously in the entity files
export enum ProcessingActivity {
  ACCESS = 'ACCESS',
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  EXPORT = 'EXPORT',
  SHARE = 'SHARE',
  ANONYMIZATION = 'ANONYMIZATION',
  ENCRYPT = 'ENCRYPT',
}

export enum ProcessingPurpose {
  SERVICE_DELIVERY = 'SERVICE_DELIVERY',
  MARKETING = 'MARKETING',
  ANALYTICS = 'ANALYTICS',
  LEGAL_OBLIGATION = 'LEGAL_OBLIGATION',
  SYSTEM_ADMINISTRATION = 'SYSTEM_ADMINISTRATION',
  SECURITY = 'SECURITY',
}

export enum ProcessingResult {
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
  PARTIAL = 'PARTIAL',
}

export interface AnonymizationConfig {
  entityType: string;
  fields: Array<{
    fieldName: string;
    method: 'redact' | 'hash' | 'generalize' | 'suppress' | 'pseudonymize';
    options?: Record<string, any>;
  }>;
  preserveStructure?: boolean;
  retainMetadata?: boolean;
}

export interface AnonymizationResult {
  success: boolean;
  entityId: string;
  fieldsProcessed: number;
  method: string;
  timestamp: Date;
  originalData?: Record<string, any>;
  anonymizedData: Record<string, any>;
  preservedFields?: string[];
  error?: string;
}

@Injectable()
export class DataAnonymizationService {
  private readonly logger = new Logger(DataAnonymizationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Anonymize data for a specific entity
   */
  async anonymizeData(
    entityType: string,
    entityId: string,
    data: Record<string, any>,
    config: AnonymizationConfig,
  ): Promise<AnonymizationResult> {
    this.logger.log(`Anonymizing data for ${entityType}:${entityId}`);

    try {
      const originalData = config.retainMetadata ? { ...data } : undefined;
      const anonymizedData = { ...data };
      const preservedFields: string[] = [];

      for (const fieldConfig of config.fields) {
        if (data.hasOwnProperty(fieldConfig.fieldName)) {
          const originalValue = data[fieldConfig.fieldName];
          const anonymizedValue = await this.anonymizeField(
            originalValue,
            fieldConfig.method,
            fieldConfig.options,
          );

          anonymizedData[fieldConfig.fieldName] = anonymizedValue;

          // Check if field was actually changed
          if (originalValue !== anonymizedValue) {
            this.logger.debug(
              `Field ${fieldConfig.fieldName} anonymized using ${fieldConfig.method}`,
            );
          } else {
            preservedFields.push(fieldConfig.fieldName);
          }
        }
      }

      // Log the anonymization action
      await this.logAnonymizationAction(entityType, entityId, config, true);

      const result: AnonymizationResult = {
        success: true,
        entityId,
        fieldsProcessed: config.fields.length,
        method: 'mixed', // Multiple methods used
        timestamp: new Date(),
        anonymizedData,
        preservedFields,
      };

      if (config.retainMetadata) {
        result.originalData = originalData;
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to anonymize data for ${entityType}:${entityId}:`,
        error,
      );

      await this.logAnonymizationAction(
        entityType,
        entityId,
        config,
        false,
        error.message,
      );

      return {
        success: false,
        entityId,
        fieldsProcessed: 0,
        method: 'error',
        timestamp: new Date(),
        anonymizedData: data,
        error: error.message,
      };
    }
  }

  /**
   * Batch anonymize multiple entities
   */
  async batchAnonymizeData(
    entityType: string,
    entities: Array<{ id: string; data: Record<string, any> }>,
    config: AnonymizationConfig,
  ): Promise<{
    totalProcessed: number;
    successful: number;
    failed: number;
    results: AnonymizationResult[];
  }> {
    this.logger.log(
      `Batch anonymizing ${entities.length} ${entityType} entities`,
    );

    const results: AnonymizationResult[] = [];
    let successful = 0;
    let failed = 0;

    for (const entity of entities) {
      const result = await this.anonymizeData(
        entityType,
        entity.id,
        entity.data,
        config,
      );
      results.push(result);

      if (result.success) {
        successful++;
      } else {
        failed++;
      }
    }

    this.logger.log(
      `Batch anonymization completed: ${successful} successful, ${failed} failed`,
    );

    return {
      totalProcessed: entities.length,
      successful,
      failed,
      results,
    };
  }

  /**
   * Get anonymization templates
   */
  getAnonymizationTemplates(): Record<string, AnonymizationConfig> {
    return {
      user_profile: {
        entityType: 'user',
        fields: [
          { fieldName: 'email', method: 'hash' },
          { fieldName: 'name', method: 'pseudonymize' },
          { fieldName: 'phone', method: 'redact' },
          {
            fieldName: 'address',
            method: 'generalize',
            options: { level: 'city' },
          },
          {
            fieldName: 'birthDate',
            method: 'generalize',
            options: { level: 'year' },
          },
        ],
        preserveStructure: true,
        retainMetadata: false,
      },
      user_activity: {
        entityType: 'activity',
        fields: [
          { fieldName: 'userId', method: 'hash' },
          {
            fieldName: 'ipAddress',
            method: 'generalize',
            options: { level: 'subnet' },
          },
          {
            fieldName: 'userAgent',
            method: 'generalize',
            options: { level: 'browser' },
          },
          { fieldName: 'sessionId', method: 'hash' },
        ],
        preserveStructure: true,
        retainMetadata: false,
      },
      communication: {
        entityType: 'communication',
        fields: [
          { fieldName: 'recipient', method: 'hash' },
          { fieldName: 'content', method: 'redact' },
          { fieldName: 'metadata', method: 'suppress' },
        ],
        preserveStructure: true,
        retainMetadata: false,
      },
      payment_info: {
        entityType: 'payment',
        fields: [
          { fieldName: 'cardNumber', method: 'redact' },
          { fieldName: 'cvv', method: 'suppress' },
          { fieldName: 'expiryDate', method: 'suppress' },
          {
            fieldName: 'amount',
            method: 'generalize',
            options: { level: 'range' },
          },
        ],
        preserveStructure: true,
        retainMetadata: false,
      },
    };
  }

  /**
   * Validate anonymization config
   */
  validateAnonymizationConfig(config: AnonymizationConfig): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!config.entityType) {
      errors.push('Entity type is required');
    }

    if (!config.fields || config.fields.length === 0) {
      errors.push('At least one field configuration is required');
    }

    const supportedMethods = [
      'redact',
      'hash',
      'generalize',
      'suppress',
      'pseudonymize',
    ];

    for (const field of config.fields || []) {
      if (!field.fieldName) {
        errors.push('Field name is required for all field configurations');
      }

      if (!supportedMethods.includes(field.method)) {
        errors.push(`Unsupported anonymization method: ${field.method}`);
      }

      if (field.method === 'generalize' && !field.options?.level) {
        warnings.push(
          `Generalization level not specified for field: ${field.fieldName}`,
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Generate anonymization report
   */
  async generateAnonymizationReport(dateRange?: {
    start: Date;
    end: Date;
  }): Promise<{
    reportDate: Date;
    period: { start: Date; end: Date };
    statistics: {
      totalEntitiesProcessed: number;
      entitiesByType: Record<string, number>;
      methodsUsed: Record<string, number>;
      successRate: number;
    };
    recentActions: Array<{
      timestamp: Date;
      entityType: string;
      entityId: string;
      fieldsProcessed: number;
      success: boolean;
    }>;
  }> {
    const period = dateRange || {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      end: new Date(),
    };

    // Get anonymization logs from the period
    const logs = await this.prisma.dataProcessingLog.findMany({
      where: {
        operation: 'data_anonymization',
        timestamp: {
          gte: period.start,
          lte: period.end,
        },
      },
      orderBy: { timestamp: 'desc' },
      take: 1000,
    });

    const statistics = {
      totalEntitiesProcessed: logs.length,
      entitiesByType: logs.reduce(
        (acc, log) => {
          acc[log.entityType] = (acc[log.entityType] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ),
      methodsUsed: logs.reduce(
        (acc, log) => {
          const details = log.details as any || {};
          const method = details.method || 'unknown';
          acc[method] = (acc[method] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ),
      successRate:
        logs.length > 0
          ? (logs.filter((log) => {
              const details = log.details as any || {};
              return details.success;
            }).length / logs.length) * 100
          : 0,
    };

    const recentActions = logs.slice(0, 50).map((log) => {
      const details = log.details as any || {};
      return {
        timestamp: log.timestamp,
        entityType: log.entityType,
        entityId: log.entityId || '',
        fieldsProcessed: details.fieldsProcessed || 0,
        success: details.success || false,
      };
    });

    return {
      reportDate: new Date(),
      period,
      statistics,
      recentActions,
    };
  }

  private async anonymizeField(
    value: any,
    method: string,
    options?: Record<string, any>,
  ): Promise<any> {
    if (value === null || value === undefined) {
      return value;
    }

    switch (method) {
      case 'redact':
        return this.redactValue(value, options);
      case 'hash':
        return this.hashValue(value, options);
      case 'generalize':
        return this.generalizeValue(value, options);
      case 'suppress':
        return null;
      case 'pseudonymize':
        return this.pseudonymizeValue(value, options);
      default:
        this.logger.warn(`Unknown anonymization method: ${method}`);
        return value;
    }
  }

  private redactValue(value: any, options?: Record<string, any>): string {
    const str = String(value);
    const redactChar = options?.char || '*';
    const preserveLength = options?.preserveLength !== false;
    const preserveFormat = options?.preserveFormat || false;

    if (preserveFormat && str.includes('@')) {
      // Email format preservation
      const [local, domain] = str.split('@');
      return `${redactChar.repeat(local.length)}@${domain}`;
    }

    if (preserveFormat && /^\d{4}-?\d{4}-?\d{4}-?\d{4}$/.test(str)) {
      // Credit card format preservation
      return str.replace(/\d/g, redactChar);
    }

    return preserveLength
      ? redactChar.repeat(str.length)
      : redactChar.repeat(8);
  }

  private hashValue(value: any, options?: Record<string, any>): string {
    // Simple hash implementation - in production, use a proper crypto library
    const str = String(value);
    const salt = options?.salt || 'default_salt';
    let hash = 0;
    const combined = str + salt;

    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(16);
  }

  private generalizeValue(value: any, options?: Record<string, any>): any {
    const level = options?.level || 'low';

    if (typeof value === 'string' && value.includes('@')) {
      // Email generalization
      const [, domain] = value.split('@');
      return level === 'high' ? `*@${domain}` : `user@${domain}`;
    }

    if (
      typeof value === 'string' &&
      /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(value)
    ) {
      // IP address generalization
      const parts = value.split('.');
      switch (level) {
        case 'subnet':
          return `${parts[0]}.${parts[1]}.${parts[2]}.0`;
        case 'network':
          return `${parts[0]}.${parts[1]}.0.0`;
        default:
          return `${parts[0]}.0.0.0`;
      }
    }

    if (value instanceof Date) {
      // Date generalization
      switch (level) {
        case 'year':
          return new Date(value.getFullYear(), 0, 1);
        case 'month':
          return new Date(value.getFullYear(), value.getMonth(), 1);
        default:
          return value;
      }
    }

    if (typeof value === 'number') {
      // Numeric generalization
      switch (level) {
        case 'range':
          const range = options?.range || 100;
          return Math.floor(value / range) * range;
        case 'order':
          return Math.floor(Math.log10(value));
        default:
          return Math.round(value / 10) * 10;
      }
    }

    return value;
  }

  private pseudonymizeValue(value: any, options?: Record<string, any>): string {
    // Simple pseudonymization - maps values consistently
    const str = String(value);
    const seed = options?.seed || 12345;

    // Generate consistent pseudonym based on input
    let hash = seed;
    for (let i = 0; i < str.length; i++) {
      hash = ((hash << 5) - hash + str.charCodeAt(i)) & 0xffffffff;
    }

    const adjectives = [
      'Red',
      'Blue',
      'Green',
      'Yellow',
      'Purple',
      'Orange',
      'Pink',
      'Brown',
    ];
    const nouns = [
      'Cat',
      'Dog',
      'Bird',
      'Fish',
      'Lion',
      'Tiger',
      'Bear',
      'Wolf',
    ];

    const adjIndex = Math.abs(hash) % adjectives.length;
    const nounIndex = Math.abs(hash >> 8) % nouns.length;
    const number = Math.abs(hash >> 16) % 1000;

    return `${adjectives[adjIndex]}${nouns[nounIndex]}${number}`;
  }

  private async logAnonymizationAction(
    entityType: string,
    entityId: string,
    config: AnonymizationConfig,
    success: boolean,
    errorMessage?: string,
  ): Promise<void> {
    await this.prisma.dataProcessingLog.create({
      data: {
        userId: 'system', // System-initiated anonymization
        operation: ProcessingActivity.ANONYMIZATION,
        entityType,
        entityId,
        purpose: ProcessingPurpose.LEGAL_OBLIGATION,
        legalBasis: 'GDPR Article 17 - Right to erasure',
        dataTypes: [entityType],
        details: {
          dataSubject: entityId,
          dataFields: config.fields.map((f) => f.fieldName).join(', '),
          systemComponent: 'gdpr-anonymization-service',
          result: success ? ProcessingResult.SUCCESS : ProcessingResult.FAILURE,
          resultDetails: errorMessage,
          isAutomated: true,
          hasUserConsent: false,
          involvesSpecialCategories: false,
          fieldsProcessed: config.fields.length,
          methods: config.fields.map((f) => f.method),
          preserveStructure: config.preserveStructure,
          retainMetadata: config.retainMetadata,
          success,
          ...(errorMessage && { error: errorMessage }),
        },
      },
    });
  }

  async anonymizeUserData(userId: string): Promise<void> {
    this.logger.log(`Starting user data anonymization for user ${userId}`);

    try {
      // This would implement the actual anonymization logic for all user data
      // For now, we'll create a processing log entry
      await this.prisma.dataProcessingLog.create({
        data: {
          userId: 'system',
          operation: ProcessingActivity.ANONYMIZATION,
          entityType: 'user',
          entityId: userId,
          purpose: ProcessingPurpose.LEGAL_OBLIGATION,
          legalBasis: 'GDPR Article 17 - Right to erasure',
          dataTypes: ['all_user_data'],
          details: {
            dataSubject: userId,
            systemComponent: 'gdpr-anonymization-service',
            result: ProcessingResult.SUCCESS,
            isAutomated: true,
            hasUserConsent: false,
            involvesSpecialCategories: false,
            action: 'full_user_anonymization',
            success: true,
          },
        },
      });

      this.logger.log(`Successfully anonymized data for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to anonymize user data for ${userId}:`, error);
      throw error;
    }
  }
}
