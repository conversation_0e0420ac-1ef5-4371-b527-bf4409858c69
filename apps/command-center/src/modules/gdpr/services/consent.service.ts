import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConsentRecord } from '@prisma/client';

// Define enums that were previously in the entity file
export enum ConsentType {
  MARKETING = 'MARKETING',
  ANALYTICS = 'ANALYTICS',
  FUNCTIONAL = 'FUNCTIONAL',
  NECESSARY = 'NECESSARY',
  PERSONALIZATION = 'PERSONALIZATION',
  THIRD_PARTY = 'THIRD_PARTY',
}

export enum ConsentStatus {
  GIVEN = 'given',
  WITHDRAWN = 'withdrawn',
  EXPIRED = 'expired',
}

export enum LegalBasis {
  CONSENT = 'CONSENT',
  CONTRACT = 'CONTRACT',
  LEGAL_OBLIGATION = 'LEGAL_OBLIGATION',
  VITAL_INTERESTS = 'VITAL_INTERESTS',
  PUBLIC_TASK = 'PUBLIC_TASK',
  LEGITIMATE_INTERESTS = 'LEGITIMATE_INTERESTS',
}

export interface ConsentRequest {
  userId: string;
  consentType: ConsentType;
  purpose: string;
  description?: string;
  legalBasis?: LegalBasis;
  expirationDate?: Date;
  source: string;
  ipAddress?: string;
  userAgent?: string;
  version?: string;
  isDoubleOptIn?: boolean;
}

export interface ConsentUpdateRequest {
  status: ConsentStatus;
  withdrawalReason?: string;
  ipAddress?: string;
  userAgent?: string;
}

// Utility functions to replace entity methods
function isConsentValid(consent: ConsentRecord): boolean {
  if (consent.status !== ConsentStatus.GIVEN) {
    return false;
  }
  
  if (consent.expiresAt && new Date(consent.expiresAt) < new Date()) {
    return false;
  }
  
  return true;
}

function canConsentBeWithdrawn(consent: ConsentRecord): boolean {
  return consent.status === ConsentStatus.GIVEN;
}

@Injectable()
export class ConsentService {
  private readonly logger = new Logger(ConsentService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async giveConsent(request: ConsentRequest): Promise<ConsentRecord> {
    this.logger.log(
      `Processing consent request for user ${request.userId}, type: ${request.consentType}`,
    );

    // Check if there's already a valid consent
    const existingConsent = await this.findLatestConsent(
      request.userId,
      request.consentType,
    );

    if (existingConsent && isConsentValid(existingConsent)) {
      this.logger.warn(
        `User ${request.userId} already has valid consent for ${request.consentType}`,
      );
      return existingConsent;
    }

    const savedConsent = await this.prisma.consentRecord.create({
      data: {
        userId: request.userId,
        consentType: request.consentType,
        status: ConsentStatus.GIVEN,
        legalBasis: request.legalBasis || LegalBasis.CONSENT,
        purpose: request.purpose,
        givenAt: new Date(),
        expiresAt: request.expirationDate,
        metadata: {
          description: request.description,
          source: request.source,
          ipAddress: request.ipAddress,
          userAgent: request.userAgent,
          version: request.version,
          isDoubleOptIn: request.isDoubleOptIn || false,
        },
      },
    });

    // Emit event for consent given
    this.eventEmitter.emit('consent.given', {
      userId: request.userId,
      consentType: request.consentType,
      consentId: savedConsent.id,
      timestamp: new Date(),
    });

    this.logger.log(
      `Consent given for user ${request.userId}, consent ID: ${savedConsent.id}`,
    );
    return savedConsent;
  }

  async withdrawConsent(
    userId: string,
    consentType: ConsentType,
    updateRequest: ConsentUpdateRequest,
  ): Promise<ConsentRecord> {
    this.logger.log(
      `Processing consent withdrawal for user ${userId}, type: ${consentType}`,
    );

    const consent = await this.findLatestConsent(userId, consentType);

    if (!consent) {
      throw new Error(
        `No consent found for user ${userId} and type ${consentType}`,
      );
    }

    if (!canConsentBeWithdrawn(consent)) {
      throw new Error(
        `Consent cannot be withdrawn for user ${userId} and type ${consentType}`,
      );
    }

    const updatedConsent = await this.prisma.consentRecord.update({
      where: { id: consent.id },
      data: {
        status: ConsentStatus.WITHDRAWN,
        withdrawnAt: new Date(),
        metadata: {
          ...(consent.metadata as any || {}),
          withdrawalReason: updateRequest.withdrawalReason,
        },
      },
    });

    // Emit event for consent withdrawal
    this.eventEmitter.emit('consent.withdrawn', {
      userId,
      consentType,
      consentId: consent.id,
      withdrawalReason: updateRequest.withdrawalReason,
      timestamp: new Date(),
    });

    this.logger.log(
      `Consent withdrawn for user ${userId}, consent ID: ${consent.id}`,
    );
    return updatedConsent;
  }

  async confirmDoubleOptIn(consentId: string): Promise<ConsentRecord> {
    this.logger.log(`Confirming double opt-in for consent ${consentId}`);

    const consent = await this.prisma.consentRecord.findUnique({
      where: { id: consentId },
    });

    if (!consent) {
      throw new Error(`Consent ${consentId} not found`);
    }

    const metadata = consent.metadata as any || {};
    
    if (!metadata.isDoubleOptIn) {
      throw new Error(
        `Consent ${consentId} is not configured for double opt-in`,
      );
    }

    if (metadata.doubleOptInConfirmedAt) {
      throw new Error(`Consent ${consentId} already confirmed`);
    }

    const updatedConsent = await this.prisma.consentRecord.update({
      where: { id: consentId },
      data: {
        metadata: {
          ...metadata,
          doubleOptInConfirmedAt: new Date(),
        },
      },
    });

    // Emit event for double opt-in confirmation
    this.eventEmitter.emit('consent.doubleOptIn.confirmed', {
      userId: consent.userId,
      consentType: consent.consentType,
      consentId: consent.id,
      timestamp: new Date(),
    });

    this.logger.log(`Double opt-in confirmed for consent ${consentId}`);
    return updatedConsent;
  }

  async hasValidConsent(
    userId: string,
    consentType: ConsentType | string,
  ): Promise<boolean> {
    const consent = await this.findLatestConsent(
      userId,
      consentType as ConsentType,
    );
    return consent ? isConsentValid(consent) : false;
  }

  async getUserConsents(userId: string): Promise<ConsentRecord[]> {
    return this.prisma.consentRecord.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getUserConsentsByType(
    userId: string,
    consentType: ConsentType,
  ): Promise<ConsentRecord[]> {
    return this.prisma.consentRecord.findMany({
      where: { userId, consentType },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getConsentHistory(
    userId: string,
    consentType: ConsentType,
  ): Promise<ConsentRecord[]> {
    return this.prisma.consentRecord.findMany({
      where: { userId, consentType },
      orderBy: { createdAt: 'asc' },
    });
  }

  async findLatestConsent(
    userId: string,
    consentType: ConsentType,
  ): Promise<ConsentRecord | null> {
    return this.prisma.consentRecord.findFirst({
      where: { userId, consentType },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getConsentSummary(
    userId: string,
  ): Promise<Record<ConsentType, boolean>> {
    const consents = await this.getUserConsents(userId);
    const summary: Record<ConsentType, boolean> = {} as any;

    // Initialize all consent types to false
    Object.values(ConsentType).forEach((type) => {
      summary[type] = false;
    });

    // Check latest consent for each type
    for (const type of Object.values(ConsentType)) {
      const latestConsent = consents.find((c) => c.consentType === type);
      summary[type] = latestConsent ? isConsentValid(latestConsent) : false;
    }

    return summary;
  }

  async getExpiringConsents(daysBefore: number = 30): Promise<ConsentRecord[]> {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + daysBefore);
    const now = new Date();

    return this.prisma.consentRecord.findMany({
      where: {
        status: ConsentStatus.GIVEN,
        expiresAt: {
          not: null,
          lte: expirationDate,
          gt: now,
        },
      },
      orderBy: { expiresAt: 'asc' },
    });
  }

  async markExpiredConsents(): Promise<number> {
    const result = await this.prisma.consentRecord.updateMany({
      where: {
        status: ConsentStatus.GIVEN,
        expiresAt: {
          lt: new Date(),
        },
      },
      data: {
        status: ConsentStatus.EXPIRED,
      },
    });

    this.logger.log(`Marked ${result.count} consents as expired`);
    return result.count;
  }

  async getConsentMetrics(startDate: Date, endDate: Date): Promise<any> {
    const consents = await this.prisma.consentRecord.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const metrics = {
      totalConsents: consents.length,
      consentsByType: {} as Record<ConsentType, number>,
      consentsByStatus: {} as Record<ConsentStatus, number>,
      consentsBySource: {} as Record<string, number>,
      averageConsentDuration: 0,
      doubleOptInRate: 0,
      withdrawalRate: 0,
    };

    // Initialize counters
    Object.values(ConsentType).forEach((type) => {
      metrics.consentsByType[type] = 0;
    });
    Object.values(ConsentStatus).forEach((status) => {
      metrics.consentsByStatus[status] = 0;
    });

    let totalDuration = 0;
    let durationCount = 0;
    let doubleOptInCount = 0;
    let withdrawnCount = 0;

    consents.forEach((consent) => {
      // Count by type
      metrics.consentsByType[consent.consentType]++;

      // Count by status
      metrics.consentsByStatus[consent.status]++;

      // Count by source
      metrics.consentsBySource[consent.source] =
        (metrics.consentsBySource[consent.source] || 0) + 1;

      // Calculate duration for withdrawn consents
      if (consent.withdrawnAt) {
        const duration =
          consent.withdrawnAt.getTime() - consent.createdAt.getTime();
        totalDuration += duration;
        durationCount++;
        withdrawnCount++;
      }

      // Count double opt-in
      const metadata = consent.metadata as any || {};
      if (metadata.isDoubleOptIn) {
        doubleOptInCount++;
      }
    });

    metrics.averageConsentDuration =
      durationCount > 0 ? totalDuration / durationCount : 0;
    metrics.doubleOptInRate =
      consents.length > 0 ? (doubleOptInCount / consents.length) * 100 : 0;
    metrics.withdrawalRate =
      consents.length > 0 ? (withdrawnCount / consents.length) * 100 : 0;

    return metrics;
  }

  async validateConsentForProcessing(
    userId: string,
    consentType: ConsentType,
    purpose: string,
  ): Promise<boolean> {
    const consent = await this.findLatestConsent(userId, consentType);

    if (!consent || !isConsentValid(consent)) {
      return false;
    }

    // Check if the purpose matches
    if (consent.purpose !== purpose) {
      this.logger.warn(
        `Consent purpose mismatch for user ${userId}: expected ${purpose}, got ${consent.purpose}`,
      );
      return false;
    }

    return true;
  }

  async renewConsent(
    userId: string,
    consentType: ConsentType,
    newExpirationDate: Date,
  ): Promise<ConsentRecord> {
    const consent = await this.findLatestConsent(userId, consentType);

    if (!consent) {
      throw new Error(
        `No consent found for user ${userId} and type ${consentType}`,
      );
    }

    // Create new consent record for renewal
    const metadata = consent.metadata as any || {};
    const savedConsent = await this.prisma.consentRecord.create({
      data: {
        userId: consent.userId,
        consentType: consent.consentType,
        status: ConsentStatus.GIVEN,
        legalBasis: consent.legalBasis,
        purpose: consent.purpose,
        givenAt: new Date(),
        expiresAt: newExpirationDate,
        metadata: {
          ...metadata,
          source: 'renewal',
        },
      },
    });

    // Emit event for consent renewal
    this.eventEmitter.emit('consent.renewed', {
      userId,
      consentType,
      oldConsentId: consent.id,
      newConsentId: savedConsent.id,
      timestamp: new Date(),
    });

    this.logger.log(
      `Consent renewed for user ${userId}, new consent ID: ${savedConsent.id}`,
    );
    return savedConsent;
  }

  async bulkWithdrawConsents(
    userId: string,
    consentTypes: ConsentType[],
  ): Promise<ConsentRecord[]> {
    const results: ConsentRecord[] = [];

    for (const consentType of consentTypes) {
      try {
        const withdrawnConsent = await this.withdrawConsent(
          userId,
          consentType,
          {
            status: ConsentStatus.WITHDRAWN,
            withdrawalReason: 'Bulk withdrawal requested by user',
          },
        );
        results.push(withdrawnConsent);
      } catch (error) {
        this.logger.error(
          `Failed to withdraw consent ${consentType} for user ${userId}:`,
          error,
        );
      }
    }

    return results;
  }

  async recordConsent(request: ConsentRequest): Promise<ConsentRecord> {
    return this.giveConsent(request);
  }

  async getConsentRecords(
    userId?: string,
    consentType?: ConsentType,
  ): Promise<ConsentRecord[]> {
    const whereClause: any = {};
    if (userId) whereClause.userId = userId;
    if (consentType) whereClause.consentType = consentType;

    return this.prisma.consentRecord.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });
  }

  async getConsentById(id: string): Promise<ConsentRecord | null> {
    return this.prisma.consentRecord.findUnique({ where: { id } });
  }

  async updateConsent(
    id: string,
    updateRequest: ConsentUpdateRequest,
  ): Promise<ConsentRecord> {
    const consent = await this.prisma.consentRecord.findUnique({ where: { id } });
    if (!consent) {
      throw new Error(`Consent ${id} not found`);
    }

    const metadata = consent.metadata as any || {};
    const updateData: any = {
      status: updateRequest.status,
    };

    if (updateRequest.status === ConsentStatus.WITHDRAWN) {
      updateData.withdrawnAt = new Date();
      updateData.metadata = {
        ...metadata,
        withdrawalReason: updateRequest.withdrawalReason,
      };
    }

    return this.prisma.consentRecord.update({
      where: { id },
      data: updateData,
    });
  }

  async withdrawUserConsents(
    userId: string,
    reason?: string,
  ): Promise<ConsentRecord[]> {
    const activeConsents = await this.prisma.consentRecord.findMany({
      where: {
        userId,
        status: ConsentStatus.GIVEN,
      },
    });

    const results: ConsentRecord[] = [];
    for (const consent of activeConsents) {
      const metadata = consent.metadata as any || {};
      const updated = await this.prisma.consentRecord.update({
        where: { id: consent.id },
        data: {
          status: ConsentStatus.WITHDRAWN,
          withdrawnAt: new Date(),
          metadata: {
            ...metadata,
            withdrawalReason: reason || 'User requested withdrawal of all consents',
          },
        },
      });
      results.push(updated);
    }

    return results;
  }

  async getUserConsentStatus(
    userId: string,
  ): Promise<Record<ConsentType, boolean>> {
    return this.getConsentSummary(userId);
  }

  async getConsentPurposes(): Promise<string[]> {
    const consents = await this.prisma.consentRecord.findMany({
      select: {
        purpose: true,
      },
      distinct: ['purpose'],
    });

    return consents.map((c) => c.purpose).filter(Boolean);
  }

  async bulkRecordConsents(
    requests: ConsentRequest[],
  ): Promise<ConsentRecord[]> {
    const results: ConsentRecord[] = [];

    for (const request of requests) {
      try {
        const consent = await this.recordConsent(request);
        results.push(consent);
      } catch (error) {
        this.logger.error(
          `Failed to record consent for user ${request.userId}:`,
          error,
        );
      }
    }

    return results;
  }

  async getConsentAnalytics(startDate: Date, endDate: Date): Promise<any> {
    return this.getConsentMetrics(startDate, endDate);
  }

  async generateConsentReport(
    userId?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    const whereClause: any = {};
    if (userId) whereClause.userId = userId;
    if (startDate && endDate) {
      whereClause.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    const consents = await this.prisma.consentRecord.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });

    const report = {
      totalConsents: consents.length,
      consentsByType: {} as Record<string, number>,
      consentsByStatus: {} as Record<string, number>,
      consents: consents,
      generatedAt: new Date(),
    };

    // Count by type and status
    consents.forEach((consent) => {
      report.consentsByType[consent.consentType] =
        (report.consentsByType[consent.consentType] || 0) + 1;
      report.consentsByStatus[consent.status] =
        (report.consentsByStatus[consent.status] || 0) + 1;
    });

    return report;
  }

  async verifyConsent(
    userId: string,
    consentType: ConsentType,
    purpose: string,
  ): Promise<boolean> {
    return this.validateConsentForProcessing(userId, consentType, purpose);
  }

  async getConsentAuditTrail(consentId: string): Promise<any[]> {
    const consent = await this.prisma.consentRecord.findUnique({
      where: { id: consentId },
    });
    if (!consent) {
      throw new Error(`Consent ${consentId} not found`);
    }

    const metadata = consent.metadata as any || {};
    
    // Return audit trail (simplified implementation)
    const auditTrail = [
      {
        action: 'consent_created',
        timestamp: consent.createdAt,
        details: {
          consentType: consent.consentType,
          status: consent.status,
          source: metadata.source || 'unknown',
        },
      },
    ];

    if (consent.withdrawnAt) {
      auditTrail.push({
        action: 'consent_withdrawn',
        timestamp: consent.withdrawnAt,
        details: {
          consentType: consent.consentType,
          status: consent.status,
          source: metadata.source || 'unknown',
          ...(metadata.withdrawalReason && {
            withdrawalReason: metadata.withdrawalReason,
          }),
        },
      });
    }

    return auditTrail;
  }
}
