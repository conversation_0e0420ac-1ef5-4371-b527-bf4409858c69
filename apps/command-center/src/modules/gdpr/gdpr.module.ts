import { Module } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';

import { GdprService } from './services/gdpr.service';
import { ConsentService } from './services/consent.service';
import { DataProtectionService } from './services/data-protection.service';
import { DataRetentionService } from './services/data-retention.service';
import { DataPortabilityService } from './services/data-portability.service';
import { DataAnonymizationService } from './services/data-anonymization.service';

import { GdprController } from './controllers/gdpr.controller';
import { ConsentController } from './controllers/consent.controller';
import { DataRequestController } from './controllers/data-request.controller';


import { GdprProcessor } from './processors/gdpr.processor';
import { DataRetentionProcessor } from './processors/data-retention.processor';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    BullModule.registerQueue(
      {
        name: 'gdpr-processing',
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 100,
        },
      },
      {
        name: 'data-retention',
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 100,
        },
      },
    ),
  ],
  controllers: [GdprController, ConsentController, DataRequestController],
  providers: [
    GdprService,
    ConsentService,
    DataProtectionService,
    DataRetentionService,
    DataPortabilityService,
    DataAnonymizationService,
    GdprProcessor,
    DataRetentionProcessor,
  ],
  exports: [
    GdprService,
    ConsentService,
    DataProtectionService,
    DataRetentionService,
    DataPortabilityService,
    DataAnonymizationService,
  ],
})
export class GdprModule {}
