import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpException,
  HttpStatus,
  ParseUUI<PERSON>ip<PERSON>,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ConversationService } from '../services/conversation.service';
import { MemoryService } from '../services/memory.service';
import {
  ConversationType,
  ConversationStatus,
  MessageRole,
  MemoryType,
} from '../../../common/types/prisma.types';

@ApiTags('ai-conversations')
@Controller('ai/conversations')
@ApiBearerAuth()
export class ConversationController {
  private readonly logger = new Logger(ConversationController.name);

  constructor(
    private readonly conversationService: ConversationService,
    private readonly memoryService: MemoryService,
  ) {}

  @Get(':id/export')
  @ApiOperation({ summary: 'Export conversation' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  @ApiQuery({ name: 'format', enum: ['json', 'txt', 'md'], required: false })
  async exportConversation(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('format') format: 'json' | 'txt' | 'md' = 'json',
  ): Promise<any> {
    const conversation = await this.conversationService.getConversation(id);
    const messages = await this.conversationService.getConversationMessages(id);

    switch (format) {
      case 'json':
        return {
          conversation: {
            id: conversation.id,
            title: conversation.title,
            description: conversation.description,
            model: conversation.model,
            createdAt: conversation.createdAt,
          },
          messages: messages.messages,
        };

      case 'md':
        const markdown = [
          `# ${conversation.title}`,
          conversation.description ? `> ${conversation.description}` : '',
          `**Model:** ${conversation.model}`,
          `**Created:** ${conversation.createdAt}`,
          '',
          '## Messages',
          '',
          ...messages.messages.map(
            (msg) =>
              `### ${msg.role === MessageRole.USER ? 'User' : 'Assistant'}\n\n${msg.content}\n`,
          ),
        ]
          .filter((line) => line !== null)
          .join('\n');
        return { content: markdown };

      case 'txt':
        const text = messages.messages
          .map((msg) => `${msg.role}: ${msg.content}`)
          .join('\n\n---\n\n');
        return { content: text };

      default:
        throw new HttpException('Invalid format', HttpStatus.BAD_REQUEST);
    }
  }

  @Post(':id/fork')
  @ApiOperation({ summary: 'Fork a conversation' })
  @ApiParam({ name: 'id', description: 'Conversation ID to fork' })
  async forkConversation(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { title?: string; fromMessageId?: string },
    @Req() req: any,
  ): Promise<any> {
    const original = await this.conversationService.getConversation(id);
    const messages = await this.conversationService.getConversationMessages(id);

    // Create new conversation
    const forked = await this.conversationService.createConversation({
      userId: req.user?.id || 'anonymous',
      title: body.title || `Fork of ${original.title}`,
      description: `Forked from conversation ${original.id}`,
      type: original.type as ConversationType,
      model: original.model,
      systemPrompt: undefined, // Will be set via systemPromptId relation
      modelConfig: original.modelConfig,
      memoryConfig: original.memoryConfig,
      tags: [...original.tags, 'forked'],
    });

    // Copy messages up to the specified point
    let messagesToCopy = messages.messages;
    if (body.fromMessageId) {
      const index = messagesToCopy.findIndex(
        (m) => m.id === body.fromMessageId,
      );
      if (index >= 0) {
        messagesToCopy = messagesToCopy.slice(0, index + 1);
      }
    }

    // Add messages to forked conversation
    for (const msg of messagesToCopy) {
      await this.conversationService.addMessage(forked.id, {
        content: msg.content,
        role: msg.role as MessageRole,
        attachments: Array.isArray(msg.attachments) ? msg.attachments : [],
        metadata: {
          ...(typeof msg.metadata === 'object' && msg.metadata ? msg.metadata : {}),
          forkedFrom: msg.id
        },
      });
    }

    return forked;
  }

  @Get(':id/summary')
  @ApiOperation({ summary: 'Get conversation summary' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  async getConversationSummary(@Param('id', ParseUUIDPipe) id: string): Promise<any> {
    const conversation = await this.conversationService.getConversation(id);
    const messages = await this.conversationService.getConversationMessages(
      id,
      { limit: 100 },
    );
    const memories = await this.memoryService.searchMemories({
      conversationId: id,
      type: MemoryType.LONG_TERM,
      limit: 10,
    });

    const stats = {
      messageCount: messages.total,
      userMessages: messages.messages.filter((m) => m.role === MessageRole.USER)
        .length,
      assistantMessages: messages.messages.filter(
        (m) => m.role === MessageRole.ASSISTANT,
      ).length,
      totalTokens: (conversation as any).metadata?.totalTokens || 0,
      duration: conversation.lastMessageAt
        ? new Date(conversation.lastMessageAt).getTime() -
          new Date(conversation.createdAt).getTime()
        : 0,
    };

    const topics = this.extractTopics(messages.messages);
    const sentiment = this.analyzeSentiment(messages.messages);

    return {
      conversation: {
        id: conversation.id,
        title: conversation.title,
        description: conversation.description,
        status: conversation.status,
        createdAt: conversation.createdAt,
        lastMessageAt: conversation.lastMessageAt,
      },
      stats,
      topics,
      sentiment,
      keyMemories: memories.map((m) => ({
        content: (m as any).summary || m.content,
        importance: m.importance,
        createdAt: m.createdAt,
      })),
    };
  }

  @Post(':id/continue-from')
  @ApiOperation({ summary: 'Continue conversation from a specific message' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  async continueFrom(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { messageId: string; newMessage: string },
  ): Promise<any> {
    // This would create a branch in the conversation
    // For now, we'll fork and continue
    const forked = await this.forkConversation(
      id,
      { fromMessageId: body.messageId },
      { user: { id: 'system' } } as any,
    );

    // Add the new message and get response
    return this.conversationService.generateResponse(
      forked.id,
      body.newMessage,
      { saveToHistory: true },
    );
  }

  @Get(':id/context')
  @ApiOperation({ summary: 'Get conversation context window' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  async getConversationContext(@Param('id', ParseUUIDPipe) id: string): Promise<any> {
    const conversation = await this.conversationService.getConversation(id);
    const recentMessages =
      await this.conversationService.getConversationMessages(id, { limit: 20 });
    const memories = await this.memoryService.searchMemories({
      conversationId: id,
      status: 'active' as any,
      limit: 10,
    });

    return {
      contextWindow: conversation.contextWindow,
      recentMessages: recentMessages.messages.map((m) => ({
        role: m.role,
        content:
          m.content.substring(0, 200) + (m.content.length > 200 ? '...' : ''),
        timestamp: m.createdAt,
      })),
      activeMemories: memories.length,
      systemPrompt: (conversation as any).systemPrompt?.content,
    };
  }

  @Post(':id/feedback')
  @ApiOperation({ summary: 'Submit conversation feedback' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  async submitFeedback(
    @Param('id', ParseUUIDPipe) id: string,
    @Body()
    body: {
      rating: number;
      feedback?: string;
      tags?: string[];
    },
  ): Promise<any> {
    const conversation = await this.conversationService.getConversation(id);

    // Store feedback in metadata
    const currentMetadata = (conversation.metadata as any) || {};
    currentMetadata.feedback = {
      rating: body.rating,
      feedback: body.feedback,
      tags: body.tags,
      timestamp: new Date(),
    };

    return this.conversationService.updateConversation(id, {
      metadata: currentMetadata,
    });
  }

  private extractTopics(messages: any[]): string[] {
    // Simple topic extraction based on frequent words
    const text = messages.map((m) => m.content).join(' ');
    const words = text.toLowerCase().split(/\s+/);
    const wordFreq = new Map<string, number>();

    const stopWords = new Set([
      'the',
      'a',
      'an',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'from',
      'up',
      'about',
      'into',
      'through',
      'during',
      'before',
      'after',
      'above',
      'below',
      'between',
      'under',
      'again',
      'further',
      'then',
      'once',
    ]);

    words.forEach((word) => {
      if (word.length > 3 && !stopWords.has(word)) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });

    return Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([word]) => word);
  }

  private analyzeSentiment(messages: any[]): {
    overall: 'positive' | 'neutral' | 'negative';
    userSentiment: 'positive' | 'neutral' | 'negative';
    trend: 'improving' | 'stable' | 'declining';
  } {
    // Simple sentiment analysis based on keywords
    const positiveWords = [
      'good',
      'great',
      'excellent',
      'amazing',
      'helpful',
      'thanks',
      'perfect',
      'awesome',
    ];
    const negativeWords = [
      'bad',
      'terrible',
      'awful',
      'horrible',
      'unhelpful',
      'wrong',
      'error',
      'failed',
    ];

    let positiveCount = 0;
    let negativeCount = 0;

    messages.forEach((msg) => {
      const content = msg.content.toLowerCase();
      positiveWords.forEach((word) => {
        if (content.includes(word)) positiveCount++;
      });
      negativeWords.forEach((word) => {
        if (content.includes(word)) negativeCount++;
      });
    });

    const overall =
      positiveCount > negativeCount
        ? 'positive'
        : negativeCount > positiveCount
          ? 'negative'
          : 'neutral';

    return {
      overall,
      userSentiment: overall, // Simplified for now
      trend: 'stable', // Would need historical data
    };
  }
}
