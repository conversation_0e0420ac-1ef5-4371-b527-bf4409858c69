import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AuditLog, Prisma } from '@prisma/client';
import {
  AuditEventType,
  AuditSeverity,
  AuditStatus,
} from '../../../common/types/prisma.types';

// Using Prisma-generated enums for type safety

export interface AuditLogEntry {
  eventType: AuditEventType;
  severity?: AuditSeverity;
  status?: AuditStatus;
  userId?: string;
  username?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource: string;
  action: string;
  description: string;
  details?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  requestId?: string;
  correlationId?: string;
  source: string;
  sourceVersion?: string;
  environment?: string;
  duration?: number;
  errorMessage?: string;
  stackTrace?: string;
  metadata?: Record<string, any>;
  isSecurityRelated?: boolean;
  isComplianceRelated?: boolean;
  requiresReview?: boolean;
  retentionDays?: number;
}

// Extended AuditLog interface with additional fields for internal use
export interface ExtendedAuditLog extends AuditLog {
  eventType?: AuditEventType;
  status?: AuditStatus;
  username?: string;
  sessionId?: string;
  resource?: string;
  description?: string;
  details?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  requestId?: string;
  correlationId?: string;
  source?: string;
  sourceVersion?: string;
  environment?: string;
  duration?: number;
  errorMessage?: string;
  stackTrace?: string;
  isSecurityRelated?: boolean;
  isComplianceRelated?: boolean;
  requiresReview?: boolean;
  retentionDays?: number;
  // timestamp inherited from base AuditLog as required field
  isReviewed?: boolean;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
  isHighSeverity?: () => boolean;
}

export interface AuditLogQuery {
  eventTypes?: AuditEventType[];
  severities?: AuditSeverity[];
  statuses?: AuditStatus[];
  userIds?: string[];
  resources?: string[];
  actions?: string[];
  sources?: string[];
  startDate?: Date;
  endDate?: Date;
  isSecurityRelated?: boolean;
  isComplianceRelated?: boolean;
  requiresReview?: boolean;
  limit?: number;
  offset?: number;
}

export interface AuditLogStats {
  totalEvents: number;
  eventsByType: Record<AuditEventType, number>;
  eventsBySeverity: Record<AuditSeverity, number>;
  eventsByStatus: Record<AuditStatus, number>;
  securityEvents: number;
  complianceEvents: number;
  eventsRequiringReview: number;
  topUsers: Array<{ userId: string; username: string; count: number }>;
  topResources: Array<{ resource: string; count: number }>;
  topActions: Array<{ action: string; count: number }>;
  averageEventsPerDay: number;
  errorRate: number;
}

@Injectable()
export class AuditLogService {
  private readonly logger = new Logger(AuditLogService.name);

  constructor(
    private readonly prisma: PrismaService,
    @InjectQueue('audit-processing')
    private readonly auditQueue: Queue,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async log(entry: AuditLogEntry): Promise<ExtendedAuditLog> {
    try {
      const severity = entry.severity || AuditSeverity.LOW;
      const timestamp = new Date();

      // Map AuditLogEntry to Prisma AuditLog schema
      const auditLogData: Prisma.AuditLogCreateInput = {
        action: entry.action,
        entityType: entry.resource,
        entityId: entry.requestId || entry.correlationId,
        userId: entry.userId,
        performedBy: entry.username || entry.userId || 'system',
        changes:
          entry.oldValues || entry.newValues
            ? JSON.stringify({
                oldValues: entry.oldValues,
                newValues: entry.newValues,
              })
            : null,
        metadata: JSON.stringify({
          eventType: entry.eventType,
          status: entry.status || AuditStatus.SUCCESS,
          description: entry.description,
          details: entry.details,
          source: entry.source,
          sourceVersion: entry.sourceVersion,
          environment:
            entry.environment || process.env.NODE_ENV || 'development',
          duration: entry.duration,
          errorMessage: entry.errorMessage,
          stackTrace: entry.stackTrace,
          sessionId: entry.sessionId,
          requestId: entry.requestId,
          correlationId: entry.correlationId,
          isSecurityRelated: entry.isSecurityRelated || false,
          isComplianceRelated: entry.isComplianceRelated || false,
          requiresReview: entry.requiresReview || false,
          retentionDays:
            entry.retentionDays ||
            this.getDefaultRetentionDays(entry.eventType),
          timestamp: timestamp,
        }),
        severity: severity,
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
      };

      const savedLog = await this.prisma.auditLog.create({
        data: auditLogData,
      });

      // Create extended audit log with additional methods
      const extendedLog = this.createExtendedAuditLog(
        savedLog,
        entry,
        severity,
        timestamp,
      );

      // Add to processing queue for analysis
      await this.auditQueue.add('analyze-audit-log', {
        auditLogId: savedLog.id,
        eventType: entry.eventType,
        severity: severity,
      });

      // Emit event for real-time monitoring
      this.eventEmitter.emit('audit.log.created', {
        id: savedLog.id,
        eventType: entry.eventType,
        severity: severity,
        isSecurityRelated: entry.isSecurityRelated || false,
        timestamp: timestamp,
      });

      // Log high severity events immediately
      if (extendedLog.isHighSeverity?.()) {
        this.logger.warn(
          `High severity audit event: ${entry.eventType} - ${entry.description}`,
        );
      }

      return extendedLog;
    } catch (error) {
      this.logger.error('Failed to create audit log:', error);
      throw error;
    }
  }

  private createExtendedAuditLog(
    auditLog: AuditLog,
    entry: AuditLogEntry,
    severity: AuditSeverity,
    timestamp: Date,
  ): ExtendedAuditLog {
    const metadata = (auditLog.metadata as any) || {};

    return {
      ...auditLog,
      eventType: entry.eventType,
      status: entry.status || AuditStatus.SUCCESS,
      username: entry.username,
      sessionId: entry.sessionId,
      resource: entry.resource,
      description: entry.description,
      details: entry.details,
      oldValues: entry.oldValues,
      newValues: entry.newValues,
      requestId: entry.requestId,
      correlationId: entry.correlationId,
      source: entry.source,
      sourceVersion: entry.sourceVersion,
      environment: entry.environment || process.env.NODE_ENV || 'development',
      duration: entry.duration,
      errorMessage: entry.errorMessage,
      stackTrace: entry.stackTrace,
      isSecurityRelated: entry.isSecurityRelated || false,
      isComplianceRelated: entry.isComplianceRelated || false,
      requiresReview: entry.requiresReview || false,
      retentionDays:
        entry.retentionDays || this.getDefaultRetentionDays(entry.eventType),
      timestamp: timestamp,
      isReviewed: metadata.isReviewed || false,
      reviewedBy: metadata.reviewedBy,
      reviewedAt: metadata.reviewedAt
        ? new Date(metadata.reviewedAt)
        : undefined,
      reviewNotes: metadata.reviewNotes,
      isHighSeverity: () =>
        severity === AuditSeverity.HIGH || severity === AuditSeverity.CRITICAL,
    };
  }

  async logSecurity(
    eventType: AuditEventType,
    description: string,
    details?: Record<string, any>,
    severity: AuditSeverity = AuditSeverity.HIGH,
  ): Promise<ExtendedAuditLog> {
    return this.log({
      eventType,
      severity,
      resource: 'security',
      action: 'security_event',
      description,
      details,
      source: 'security-system',
      isSecurityRelated: true,
      requiresReview: true,
    });
  }

  async logCompliance(
    eventType: AuditEventType,
    description: string,
    details?: Record<string, any>,
    severity: AuditSeverity = AuditSeverity.MEDIUM,
  ): Promise<ExtendedAuditLog> {
    return this.log({
      eventType,
      severity,
      resource: 'compliance',
      action: 'compliance_event',
      description,
      details,
      source: 'compliance-system',
      isComplianceRelated: true,
      requiresReview: true,
    });
  }

  async logUserActivity(
    userId: string,
    username: string,
    action: string,
    resource: string,
    description: string,
    details?: Record<string, any>,
    request?: any,
  ): Promise<ExtendedAuditLog> {
    return this.log({
      eventType: AuditEventType.DATA_ACCESS,
      userId,
      username,
      sessionId: request?.sessionId,
      ipAddress: request?.ip,
      userAgent: request?.headers?.['user-agent'],
      resource,
      action,
      description,
      details,
      requestId: request?.id,
      source: 'user-activity',
    });
  }

  async logSystemEvent(
    eventType: AuditEventType,
    description: string,
    details?: Record<string, any>,
    severity: AuditSeverity = AuditSeverity.LOW,
  ): Promise<ExtendedAuditLog> {
    return this.log({
      eventType,
      severity,
      resource: 'system',
      action: 'system_event',
      description,
      details,
      source: 'system',
    });
  }

  async logDataChange(
    userId: string,
    username: string,
    resource: string,
    action: string,
    oldValues: Record<string, any>,
    newValues: Record<string, any>,
    request?: any,
  ): Promise<ExtendedAuditLog> {
    return this.log({
      eventType:
        action === 'create'
          ? AuditEventType.DATA_CREATE
          : action === 'update'
            ? AuditEventType.DATA_UPDATE
            : AuditEventType.DATA_DELETE,
      userId,
      username,
      sessionId: request?.sessionId,
      ipAddress: request?.ip,
      userAgent: request?.headers?.['user-agent'],
      resource,
      action,
      description: `${action} ${resource}`,
      oldValues,
      newValues,
      requestId: request?.id,
      source: 'data-change',
    });
  }

  async query(queryParams: AuditLogQuery): Promise<ExtendedAuditLog[]> {
    const where: Prisma.AuditLogWhereInput = {};

    // Build metadata filters for fields stored in the metadata JSON
    const metadataFilter: any = {};

    if (queryParams.eventTypes?.length) {
      metadataFilter.eventType = { in: queryParams.eventTypes };
    }

    if (queryParams.statuses?.length) {
      metadataFilter.status = { in: queryParams.statuses };
    }

    if (queryParams.resources?.length) {
      where.entityType = { in: queryParams.resources };
    }

    if (queryParams.sources?.length) {
      metadataFilter.source = { in: queryParams.sources };
    }

    if (queryParams.isSecurityRelated !== undefined) {
      metadataFilter.isSecurityRelated = queryParams.isSecurityRelated;
    }

    if (queryParams.isComplianceRelated !== undefined) {
      metadataFilter.isComplianceRelated = queryParams.isComplianceRelated;
    }

    if (queryParams.requiresReview !== undefined) {
      metadataFilter.requiresReview = queryParams.requiresReview;
    }

    // Direct field filters
    if (queryParams.severities?.length) {
      where.severity = { in: queryParams.severities };
    }

    if (queryParams.userIds?.length) {
      where.userId = { in: queryParams.userIds };
    }

    if (queryParams.actions?.length) {
      where.action = { in: queryParams.actions };
    }

    if (queryParams.startDate && queryParams.endDate) {
      where.createdAt = {
        gte: queryParams.startDate,
        lte: queryParams.endDate,
      };
    }

    // Add metadata filter as JSON contains filter if any metadata filters exist
    if (Object.keys(metadataFilter).length > 0) {
      // Use JSON contains query for metadata filtering
      const metadataConditions = [];
      for (const [key, value] of Object.entries(metadataFilter)) {
        if (
          typeof value === 'object' &&
          value !== null &&
          'in' in value &&
          Array.isArray((value as any).in)
        ) {
          // Handle IN queries for arrays
          const inValues = (value as any).in;
          metadataConditions.push(
            ...inValues.map((v: any) => ({
              metadata: {
                contains: `"${key}":"${v}"`,
              } as any,
            })),
          );
        } else {
          metadataConditions.push({
            metadata: {
              contains: `"${key}":${typeof value === 'string' ? `"${value}"` : value}`,
            } as any,
          });
        }
      }

      if (metadataConditions.length === 1) {
        where.AND = where.AND
          ? [
              ...(Array.isArray(where.AND) ? where.AND : [where.AND]),
              metadataConditions[0],
            ]
          : metadataConditions[0];
      } else if (metadataConditions.length > 1) {
        where.AND = where.AND
          ? [
              ...(Array.isArray(where.AND) ? where.AND : [where.AND]),
              { OR: metadataConditions },
            ]
          : { OR: metadataConditions };
      }
    }

    const logs = await this.prisma.auditLog.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: queryParams.limit || undefined,
      skip: queryParams.offset || undefined,
    });

    return logs.map((log) => this.mapPrismaToExtendedAuditLog(log));
  }

  private mapPrismaToExtendedAuditLog(log: AuditLog): ExtendedAuditLog {
    const metadata = (log.metadata as any) || {};

    return {
      ...log,
      eventType: metadata.eventType,
      status: metadata.status,
      username: log.performedBy,
      sessionId: metadata.sessionId,
      resource: log.entityType || metadata.resource,
      description: metadata.description,
      details: metadata.details,
      oldValues: metadata.oldValues,
      newValues: metadata.newValues,
      requestId: metadata.requestId,
      correlationId: metadata.correlationId,
      source: metadata.source,
      sourceVersion: metadata.sourceVersion,
      environment: metadata.environment,
      duration: metadata.duration,
      errorMessage: metadata.errorMessage,
      stackTrace: metadata.stackTrace,
      isSecurityRelated: metadata.isSecurityRelated || false,
      isComplianceRelated: metadata.isComplianceRelated || false,
      requiresReview: metadata.requiresReview || false,
      retentionDays: metadata.retentionDays,
      timestamp: log.createdAt,
      isReviewed: metadata.isReviewed || false,
      reviewedBy: metadata.reviewedBy,
      reviewedAt: metadata.reviewedAt
        ? new Date(metadata.reviewedAt)
        : undefined,
      reviewNotes: metadata.reviewNotes,
      isHighSeverity: () =>
        log.severity === AuditSeverity.HIGH ||
        log.severity === AuditSeverity.CRITICAL,
    };
  }

  async getStats(startDate: Date, endDate: Date): Promise<AuditLogStats> {
    const logs = await this.prisma.auditLog.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const extendedLogs = logs.map((log) =>
      this.mapPrismaToExtendedAuditLog(log),
    );

    const stats: AuditLogStats = {
      totalEvents: extendedLogs.length,
      eventsByType: {} as Record<AuditEventType, number>,
      eventsBySeverity: {} as Record<AuditSeverity, number>,
      eventsByStatus: {} as Record<AuditStatus, number>,
      securityEvents: 0,
      complianceEvents: 0,
      eventsRequiringReview: 0,
      topUsers: [],
      topResources: [],
      topActions: [],
      averageEventsPerDay: 0,
      errorRate: 0,
    };

    // Initialize counters
    Object.values(AuditEventType).forEach((type) => {
      stats.eventsByType[type] = 0;
    });
    Object.values(AuditSeverity).forEach((severity) => {
      stats.eventsBySeverity[severity] = 0;
    });
    Object.values(AuditStatus).forEach((status) => {
      stats.eventsByStatus[status] = 0;
    });

    const userCounts: Record<
      string,
      { userId: string; username: string; count: number }
    > = {};
    const resourceCounts: Record<string, number> = {};
    const actionCounts: Record<string, number> = {};
    let errorCount = 0;

    extendedLogs.forEach((log) => {
      // Count by type
      stats.eventsByType[log.eventType]++;

      // Count by severity
      stats.eventsBySeverity[log.severity]++;

      // Count by status
      stats.eventsByStatus[log.status]++;

      // Count security events
      if (log.isSecurityRelated) {
        stats.securityEvents++;
      }

      // Count compliance events
      if (log.isComplianceRelated) {
        stats.complianceEvents++;
      }

      // Count events requiring review
      if (log.requiresReview) {
        stats.eventsRequiringReview++;
      }

      // Count users
      if (log.userId) {
        if (!userCounts[log.userId]) {
          userCounts[log.userId] = {
            userId: log.userId,
            username: log.username || 'Unknown',
            count: 0,
          };
        }
        userCounts[log.userId].count++;
      }

      // Count resources
      if (log.resource) {
        resourceCounts[log.resource] = (resourceCounts[log.resource] || 0) + 1;
      }

      // Count actions
      actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;

      // Count errors
      if (log.status === AuditStatus.FAILURE) {
        errorCount++;
      }
    });

    // Calculate top users
    stats.topUsers = Object.values(userCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate top resources
    stats.topResources = Object.entries(resourceCounts)
      .map(([resource, count]) => ({ resource, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate top actions
    stats.topActions = Object.entries(actionCounts)
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate average events per day
    const daysDiff = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
    );
    stats.averageEventsPerDay =
      daysDiff > 0 ? extendedLogs.length / daysDiff : 0;

    // Calculate error rate
    stats.errorRate =
      extendedLogs.length > 0 ? (errorCount / extendedLogs.length) * 100 : 0;

    return stats;
  }

  async getEventsRequiringReview(): Promise<ExtendedAuditLog[]> {
    const logs = await this.prisma.auditLog.findMany({
      where: {
        AND: [
          {
            metadata: {
              contains: '"requiresReview":true',
            } as any,
          },
          {
            metadata: {
              not: {
                contains: '"isReviewed":true',
              },
            } as any,
          },
        ],
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return logs.map((log) => this.mapPrismaToExtendedAuditLog(log));
  }

  async markAsReviewed(
    id: string,
    reviewedBy: string,
    reviewNotes?: string,
  ): Promise<ExtendedAuditLog> {
    const log = await this.prisma.auditLog.findUnique({ where: { id } });
    if (!log) {
      throw new Error(`Audit log ${id} not found`);
    }

    const metadata = (log.metadata as any) || {};
    metadata.isReviewed = true;
    metadata.reviewedBy = reviewedBy;
    metadata.reviewedAt = new Date().toISOString();
    metadata.reviewNotes = reviewNotes;

    const updatedLog = await this.prisma.auditLog.update({
      where: { id },
      data: {
        metadata,
      },
    });

    return this.mapPrismaToExtendedAuditLog(updatedLog);
  }

  async cleanupExpiredLogs(): Promise<number> {
    // Since retentionDays is stored in metadata, we need to find logs where the retention period has expired
    const allLogs = await this.prisma.auditLog.findMany({
      select: {
        id: true,
        createdAt: true,
        metadata: true,
      },
    });

    const expiredIds: string[] = [];
    const now = new Date();

    allLogs.forEach((log) => {
      const metadata = (log.metadata as any) || {};
      const retentionDays = metadata.retentionDays;

      if (retentionDays && retentionDays > 0) {
        const expirationDate = new Date(log.createdAt);
        expirationDate.setDate(expirationDate.getDate() + retentionDays);

        if (now > expirationDate) {
          expiredIds.push(log.id);
        }
      }
    });

    if (expiredIds.length === 0) {
      return 0;
    }

    await this.prisma.auditLog.deleteMany({
      where: {
        id: {
          in: expiredIds,
        },
      },
    });

    this.logger.log(`Cleaned up ${expiredIds.length} expired audit logs`);
    return expiredIds.length;
  }

  async exportLogs(
    startDate: Date,
    endDate: Date,
    format: 'json' | 'csv' = 'json',
  ): Promise<string> {
    const logs = await this.prisma.auditLog.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const extendedLogs = logs.map((log) =>
      this.mapPrismaToExtendedAuditLog(log),
    );

    if (format === 'json') {
      return JSON.stringify(extendedLogs, null, 2);
    }

    // CSV format
    const headers = [
      'ID',
      'Event Type',
      'Severity',
      'Status',
      'User ID',
      'Username',
      'IP Address',
      'Resource',
      'Action',
      'Description',
      'Timestamp',
    ];

    const csvRows = [
      headers.join(','),
      ...extendedLogs.map((log) =>
        [
          log.id,
          log.eventType || '',
          log.severity,
          log.status || '',
          log.userId || '',
          log.username || '',
          log.ipAddress || '',
          log.resource || '',
          log.action,
          `"${(log.description || '').replace(/"/g, '""')}"`,
          (log.timestamp || log.createdAt).toISOString(),
        ].join(','),
      ),
    ];

    return csvRows.join('\n');
  }

  private getDefaultRetentionDays(eventType: AuditEventType): number {
    // Security and compliance events have longer retention
    if (this.isSecurityEvent(eventType) || this.isComplianceEvent(eventType)) {
      return 2555; // 7 years
    }

    // Regular events
    switch (eventType) {
      case AuditEventType.LOGIN:
      case AuditEventType.LOGOUT:
        return 90; // 3 months
      case AuditEventType.DATA_ACCESS:
      case AuditEventType.DATA_VIEW:
        return 365; // 1 year
      case AuditEventType.DATA_CREATE:
      case AuditEventType.DATA_UPDATE:
      case AuditEventType.DATA_DELETE:
        return 2555; // 7 years
      default:
        return 365; // 1 year
    }
  }

  private isSecurityEvent(eventType: AuditEventType): boolean {
    const securityEvents = [
      'LOGIN_FAILED',
      'ACCOUNT_LOCKED', 
      'SECURITY_VIOLATION',
      'PERMISSION_DENIED',
      'UNAUTHORIZED_ACCESS',
      'SUSPICIOUS_ACTIVITY',
      'SECURITY_SCAN',
    ];
    return securityEvents.includes(eventType as string);
  }

  private isComplianceEvent(eventType: AuditEventType): boolean {
    const complianceEvents = [
      'CONSENT_GIVEN',
      'CONSENT_WITHDRAWN',
      'DATA_RETENTION_EXECUTED',
      'DATA_ANONYMIZED', 
      'COMPLIANCE_REPORT_GENERATED',
    ];
    return complianceEvents.includes(eventType as string);
  }

  async searchLogs(
    searchTerm: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<ExtendedAuditLog[]> {
    const where: Prisma.AuditLogWhereInput = {
      OR: [
        {
          action: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          entityType: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          metadata: {
            contains: searchTerm,
          } as any,
        },
        {
          performedBy: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
      ],
    };

    if (startDate && endDate) {
      where.createdAt = {
        gte: startDate,
        lte: endDate,
      };
    }

    const logs = await this.prisma.auditLog.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: 100,
    });

    return logs.map((log) => this.mapPrismaToExtendedAuditLog(log));
  }

  async getAuditTrail(
    resource: string,
    resourceId: string,
  ): Promise<ExtendedAuditLog[]> {
    const logs = await this.prisma.auditLog.findMany({
      where: {
        OR: [
          {
            entityType: resource,
            entityId: resourceId,
          },
          {
            entityType: resource,
            metadata: {
              contains: `"resourceId":"${resourceId}"`,
            } as any,
          },
          {
            entityType: resource,
            changes: {
              contains: resourceId,
            } as any,
          },
        ],
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return logs.map((log) => this.mapPrismaToExtendedAuditLog(log));
  }

  async getAuditLogs(query?: AuditLogQuery): Promise<ExtendedAuditLog[]> {
    if (!query) {
      const logs = await this.prisma.auditLog.findMany({
        orderBy: { createdAt: 'desc' },
        take: 100,
      });
      return logs.map((log) => this.mapPrismaToExtendedAuditLog(log));
    }
    return this.query(query);
  }

  async getAuditLogById(id: string): Promise<ExtendedAuditLog | null> {
    const log = await this.prisma.auditLog.findUnique({ where: { id } });
    return log ? this.mapPrismaToExtendedAuditLog(log) : null;
  }

  async createAuditLog(entry: AuditLogEntry): Promise<ExtendedAuditLog> {
    return this.log(entry);
  }

  async cleanupOldLogs(retentionDays?: number): Promise<number> {
    if (retentionDays) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const expiredLogs = await this.prisma.auditLog.findMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
        select: {
          id: true,
        },
      });

      if (expiredLogs.length === 0) {
        return 0;
      }

      const expiredIds = expiredLogs.map((log) => log.id);
      await this.prisma.auditLog.deleteMany({
        where: {
          id: {
            in: expiredIds,
          },
        },
      });

      this.logger.log(
        `Cleaned up ${expiredLogs.length} audit logs older than ${retentionDays} days`,
      );
      return expiredLogs.length;
    }

    return this.cleanupExpiredLogs();
  }
}
