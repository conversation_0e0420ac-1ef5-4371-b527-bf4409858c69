import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../database/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { <PERSON>ron } from '@nestjs/schedule';
import {
  Prisma,
  AuditLog,
  User,
  UserSession,
  AuthAttempt,
  AuditEventType,
  AuditSeverity,
} from '../../../common/types/prisma.types';
import { AuditLogService } from './audit-log.service';

// Security Event Types - these would typically be stored as enum values in metadata
export enum SecurityEventType {
  BRUTE_FORCE_ATTACK = 'brute_force_attack',
  SQL_INJECTION_ATTEMPT = 'sql_injection_attempt',
  XSS_ATTEMPT = 'xss_attempt',
  CSRF_ATTEMPT = 'csrf_attempt',
  DDoS_ATTACK = 'ddos_attack',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  UNAUTHORIZED_FILE_ACCESS = 'unauthorized_file_access',
  SUSPICIOUS_FILE_UPLOAD = 'suspicious_file_upload',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  MALWARE_DETECTED = 'malware_detected',
  SUSPICIOUS_LOGIN_PATTERN = 'suspicious_login_pattern',
  ACCOUNT_ENUMERATION = 'account_enumeration',
  POLICY_VIOLATION = 'policy_violation',
  DATA_EXFILTRATION_ATTEMPT = 'data_exfiltration_attempt',
}

export enum SecurityThreatLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum SecurityEventStatus {
  ACTIVE = 'active',
  INVESTIGATING = 'investigating',
  MITIGATED = 'mitigated',
  RESOLVED = 'resolved',
  FALSE_POSITIVE = 'false_positive',
}

// SecurityEvent interface using AuditLog as the base model
export interface SecurityEvent {
  id: string;
  eventType: SecurityEventType;
  threatLevel: SecurityThreatLevel;
  status: SecurityEventStatus;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  userId?: string;
  details?: Record<string, any>;
  isBlocked: boolean;
  resolvedAt?: Date;
  mitigationActions?: string;
  requiresInvestigation?: boolean;

  // Helper methods
  isResolved: () => boolean;
  isActive: () => boolean;
  isRecurring: () => boolean;
  shouldAutoRemediate: () => boolean;
  escalationRequired: () => boolean;
  getAgeInMinutes: () => number;
}

export interface SecurityAuditReport {
  auditDate: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  summary: {
    totalEvents: number;
    criticalEvents: number;
    highThreatEvents: number;
    resolvedEvents: number;
    activeThreats: number;
    averageResponseTime: number;
    topThreatTypes: Array<{ type: SecurityEventType; count: number }>;
    topAttackSources: Array<{ source: string; count: number }>;
  };
  trends: {
    eventTrend: Array<{ date: string; count: number }>;
    threatLevelTrend: Array<{
      date: string;
      critical: number;
      high: number;
      medium: number;
      low: number;
    }>;
  };
  recommendations: string[];
  details: {
    activeThreats: SecurityEvent[];
    recentIncidents: SecurityEvent[];
    failedLogins: number;
    suspiciousActivities: number;
    blockedRequests: number;
  };
}

export interface ThreatIntelligence {
  ipAddress: string;
  threatLevel: SecurityThreatLevel;
  eventCount: number;
  firstSeen: Date;
  lastSeen: Date;
  eventTypes: SecurityEventType[];
  isBlocked: boolean;
  country?: string;
  organization?: string;
  reputation?: number;
}

@Injectable()
export class SecurityAuditService {
  private readonly logger = new Logger(SecurityAuditService.name);
  private readonly threatIntelligenceCache = new Map<
    string,
    ThreatIntelligence
  >();

  constructor(
    private readonly prisma: PrismaService,
    private readonly auditLogService: AuditLogService,
    private readonly configService: ConfigService,
  ) {}

  async generateSecurityAuditReport(
    startDate: Date,
    endDate: Date,
  ): Promise<SecurityAuditReport> {
    this.logger.log(
      `Generating security audit report for ${startDate} to ${endDate}`,
    );

    // Get security events from AuditLog where metadata contains security-related information
    const auditLogs = await this.prisma.auditLog.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        metadata: {
          contains: '"isSecurityRelated":true',
        } as any,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Convert audit logs to security events
    const events = auditLogs.map((log) => this.mapAuditLogToSecurityEvent(log));

    const report: SecurityAuditReport = {
      auditDate: new Date(),
      timeRange: { start: startDate, end: endDate },
      summary: await this.generateSummary(events),
      trends: await this.generateTrends(events, startDate, endDate),
      recommendations: await this.generateRecommendations(events),
      details: await this.generateDetails(events),
    };

    // Log the report generation
    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_SCAN,
      'Security audit report generated',
      {
        reportPeriod: { start: startDate, end: endDate },
        eventCount: events.length,
      },
      AuditSeverity.LOW,
    );

    return report;
  }

  private async generateSummary(
    events: SecurityEvent[],
  ): Promise<SecurityAuditReport['summary']> {
    const criticalEvents = events.filter(
      (e) => e.threatLevel === SecurityThreatLevel.CRITICAL,
    );
    const highThreatEvents = events.filter(
      (e) => e.threatLevel === SecurityThreatLevel.HIGH,
    );
    const resolvedEvents = events.filter((e) => e.isResolved());
    const activeThreats = events.filter((e) => e.isActive());

    // Calculate average response time
    const resolvedEventsWithTime = resolvedEvents.filter((e) => e.resolvedAt);
    const averageResponseTime =
      resolvedEventsWithTime.length > 0
        ? resolvedEventsWithTime.reduce((sum, event) => {
            return (
              sum + (event.resolvedAt!.getTime() - event.timestamp.getTime())
            );
          }, 0) / resolvedEventsWithTime.length
        : 0;

    // Top threat types
    const threatTypeCounts: Record<SecurityEventType, number> = {} as any;
    events.forEach((event) => {
      threatTypeCounts[event.eventType] =
        (threatTypeCounts[event.eventType] || 0) + 1;
    });

    const topThreatTypes = Object.entries(threatTypeCounts)
      .map(([type, count]) => ({ type: type as SecurityEventType, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Top attack sources
    const sourceCounts: Record<string, number> = {};
    events.forEach((event) => {
      if (event.ipAddress) {
        sourceCounts[event.ipAddress] =
          (sourceCounts[event.ipAddress] || 0) + 1;
      }
    });

    const topAttackSources = Object.entries(sourceCounts)
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalEvents: events.length,
      criticalEvents: criticalEvents.length,
      highThreatEvents: highThreatEvents.length,
      resolvedEvents: resolvedEvents.length,
      activeThreats: activeThreats.length,
      averageResponseTime: Math.round(averageResponseTime / 1000 / 60), // Convert to minutes
      topThreatTypes,
      topAttackSources,
    };
  }

  private async generateTrends(
    events: SecurityEvent[],
    startDate: Date,
    endDate: Date,
  ): Promise<SecurityAuditReport['trends']> {
    const daysDiff = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
    );
    const eventTrend: Array<{ date: string; count: number }> = [];
    const threatLevelTrend: Array<{
      date: string;
      critical: number;
      high: number;
      medium: number;
      low: number;
    }> = [];

    for (let i = 0; i < daysDiff; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      const dayEvents = events.filter((event) => {
        const eventDate = new Date(event.timestamp);
        return eventDate.toISOString().split('T')[0] === dateStr;
      });

      eventTrend.push({
        date: dateStr,
        count: dayEvents.length,
      });

      threatLevelTrend.push({
        date: dateStr,
        critical: dayEvents.filter(
          (e) => e.threatLevel === SecurityThreatLevel.CRITICAL,
        ).length,
        high: dayEvents.filter(
          (e) => e.threatLevel === SecurityThreatLevel.HIGH,
        ).length,
        medium: dayEvents.filter(
          (e) => e.threatLevel === SecurityThreatLevel.MEDIUM,
        ).length,
        low: dayEvents.filter((e) => e.threatLevel === SecurityThreatLevel.LOW)
          .length,
      });
    }

    return { eventTrend, threatLevelTrend };
  }

  private async generateRecommendations(
    events: SecurityEvent[],
  ): Promise<string[]> {
    const recommendations: string[] = [];
    const activeThreats = events.filter((e) => e.isActive());
    const criticalEvents = events.filter(
      (e) => e.threatLevel === SecurityThreatLevel.CRITICAL,
    );
    const recurringEvents = events.filter((e) => e.isRecurring());

    if (activeThreats.length > 0) {
      recommendations.push(
        `${activeThreats.length} active security threats require immediate attention`,
      );
    }

    if (criticalEvents.length > 0) {
      recommendations.push(
        `${criticalEvents.length} critical security events need investigation`,
      );
    }

    if (recurringEvents.length > 0) {
      recommendations.push(
        `${recurringEvents.length} recurring security events suggest persistent threats`,
      );
    }

    // Check for brute force patterns
    const bruteForceEvents = events.filter(
      (e) => e.eventType === SecurityEventType.BRUTE_FORCE_ATTACK,
    );
    if (bruteForceEvents.length > 10) {
      recommendations.push(
        'High number of brute force attacks detected - consider implementing rate limiting',
      );
    }

    // Check for injection attempts
    const injectionEvents = events.filter(
      (e) =>
        e.eventType === SecurityEventType.SQL_INJECTION_ATTEMPT ||
        e.eventType === SecurityEventType.XSS_ATTEMPT,
    );
    if (injectionEvents.length > 5) {
      recommendations.push(
        'Multiple injection attempts detected - review input validation and sanitization',
      );
    }

    // Check for privilege escalation
    const privilegeEscalationEvents = events.filter(
      (e) => e.eventType === SecurityEventType.PRIVILEGE_ESCALATION,
    );
    if (privilegeEscalationEvents.length > 0) {
      recommendations.push(
        'Privilege escalation attempts detected - review access controls and permissions',
      );
    }

    // Check response times
    const unresolvedEvents = events.filter((e) => !e.isResolved());
    const overdueEvents = unresolvedEvents.filter((e) =>
      e.escalationRequired(),
    );
    if (overdueEvents.length > 0) {
      recommendations.push(
        `${overdueEvents.length} security events are overdue for resolution`,
      );
    }

    return recommendations;
  }

  private async generateDetails(
    events: SecurityEvent[],
  ): Promise<SecurityAuditReport['details']> {
    const activeThreats = events.filter((e) => e.isActive()).slice(0, 10);
    const recentIncidents = events.filter((e) => e.isResolved()).slice(0, 10);

    // Get additional metrics from audit logs
    const failedLogins = await this.prisma.auditLog.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          lte: new Date(),
        },
        metadata: {
          contains: `"eventType":"${AuditEventType.LOGIN_FAILED}"`,
        } as any,
      },
    });

    const suspiciousActivities = await this.prisma.auditLog.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          lte: new Date(),
        },
        metadata: {
          contains: `"eventType":"${AuditEventType.SUSPICIOUS_ACTIVITY}"`,
        } as any,
      },
    });

    const blockedRequests = events.filter((e) => e.isBlocked).length;

    return {
      activeThreats,
      recentIncidents,
      failedLogins,
      suspiciousActivities,
      blockedRequests,
    };
  }

  async analyzeSecurityEvent(event: SecurityEvent): Promise<void> {
    this.logger.log(
      `Analyzing security event: ${event.eventType} from ${event.ipAddress}`,
    );

    // Update threat intelligence
    if (event.ipAddress) {
      await this.updateThreatIntelligence(event);
    }

    // Check for patterns
    await this.checkForSecurityPatterns(event);

    // Auto-respond if configured
    if (event.shouldAutoRemediate()) {
      await this.autoRemediateSecurityEvent(event);
    }

    // Escalate if required
    if (event.escalationRequired()) {
      await this.escalateSecurityEvent(event);
    }
  }

  private async updateThreatIntelligence(event: SecurityEvent): Promise<void> {
    if (!event.ipAddress) return;

    let intelligence = this.threatIntelligenceCache.get(event.ipAddress);

    if (!intelligence) {
      intelligence = {
        ipAddress: event.ipAddress,
        threatLevel: event.threatLevel,
        eventCount: 0,
        firstSeen: event.timestamp,
        lastSeen: event.timestamp,
        eventTypes: [],
        isBlocked: false,
      };
    }

    intelligence.eventCount++;
    intelligence.lastSeen = event.timestamp;

    if (!intelligence.eventTypes.includes(event.eventType)) {
      intelligence.eventTypes.push(event.eventType);
    }

    // Update threat level to highest observed
    if (
      this.getThreatLevelWeight(event.threatLevel) >
      this.getThreatLevelWeight(intelligence.threatLevel)
    ) {
      intelligence.threatLevel = event.threatLevel;
    }

    // Auto-block if threshold exceeded
    if (
      intelligence.eventCount >= 10 &&
      intelligence.threatLevel === SecurityThreatLevel.HIGH
    ) {
      intelligence.isBlocked = true;
      await this.blockThreatSource(event.ipAddress);
    }

    this.threatIntelligenceCache.set(event.ipAddress, intelligence);
  }

  private getThreatLevelWeight(level: SecurityThreatLevel): number {
    switch (level) {
      case SecurityThreatLevel.LOW:
        return 1;
      case SecurityThreatLevel.MEDIUM:
        return 2;
      case SecurityThreatLevel.HIGH:
        return 3;
      case SecurityThreatLevel.CRITICAL:
        return 4;
      default:
        return 0;
    }
  }

  private async checkForSecurityPatterns(event: SecurityEvent): Promise<void> {
    // Find recent security events of the same type from the same IP
    const recentEvents = await this.prisma.auditLog.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
          lte: new Date(),
        },
        ipAddress: event.ipAddress,
        metadata: {
          contains: `"eventType":"${event.eventType}"`,
        } as any,
      },
      orderBy: { createdAt: 'desc' },
    });

    // Check for brute force pattern
    if (recentEvents.length >= 5) {
      await this.auditLogService.logSecurity(
        AuditEventType.SUSPICIOUS_ACTIVITY,
        `Potential brute force attack detected from ${event.ipAddress}`,
        { eventCount: recentEvents.length, eventType: event.eventType },
        AuditSeverity.HIGH,
      );
    }

    // Check for distributed attack pattern
    const sameTypeEvents = await this.prisma.auditLog.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 60 * 1000), // Last 30 minutes
          lte: new Date(),
        },
        metadata: {
          contains: `"eventType":"${event.eventType}"`,
        } as any,
      },
    });

    const uniqueIPs = new Set(
      sameTypeEvents.map((e) => e.ipAddress).filter((ip) => ip),
    );
    if (uniqueIPs.size >= 10) {
      await this.auditLogService.logSecurity(
        AuditEventType.SUSPICIOUS_ACTIVITY,
        `Potential distributed attack detected: ${event.eventType}`,
        { uniqueIPs: uniqueIPs.size, eventCount: sameTypeEvents.length },
        AuditSeverity.CRITICAL,
      );
    }
  }

  private async autoRemediateSecurityEvent(
    event: SecurityEvent,
  ): Promise<void> {
    this.logger.log(`Auto-remediating security event: ${event.id}`);

    try {
      switch (event.eventType) {
        case SecurityEventType.BRUTE_FORCE_ATTACK:
          if (event.ipAddress) {
            await this.blockThreatSource(event.ipAddress);
          }
          break;
        case SecurityEventType.RATE_LIMIT_EXCEEDED:
          // Rate limiting is typically handled by middleware
          break;
        case SecurityEventType.SUSPICIOUS_FILE_UPLOAD:
          await this.quarantineFile(event.details?.fileName);
          break;
        default:
          this.logger.warn(
            `No auto-remediation available for event type: ${event.eventType}`,
          );
      }

      // Update event with remediation details in audit log metadata
      await this.updateSecurityEventStatus(event.id, {
        status: SecurityEventStatus.MITIGATED,
        mitigationActions: 'Auto-remediated by security audit service',
        responseDetails: { autoRemediated: true, timestamp: new Date() },
      });

      await this.auditLogService.logSecurity(
        AuditEventType.SECURITY_VIOLATION,
        `Auto-remediated security event: ${event.eventType}`,
        { eventId: event.id, eventType: event.eventType },
        AuditSeverity.MEDIUM,
      );
    } catch (error) {
      this.logger.error(
        `Failed to auto-remediate security event ${event.id}:`,
        error,
      );
    }
  }

  private async escalateSecurityEvent(event: SecurityEvent): Promise<void> {
    this.logger.warn(`Escalating security event: ${event.id}`);

    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_VIOLATION,
      `Security event escalated: ${event.eventType}`,
      {
        eventId: event.id,
        eventType: event.eventType,
        threatLevel: event.threatLevel,
        ageInMinutes: event.getAgeInMinutes(),
      },
      AuditSeverity.HIGH,
    );

    // Update event status
    await this.updateSecurityEventStatus(event.id, {
      status: SecurityEventStatus.INVESTIGATING,
      requiresInvestigation: true,
    });

    // Here you would typically send notifications to security team
    // await this.notificationService.sendSecurityAlert(event);
  }

  private async blockThreatSource(ipAddress: string): Promise<void> {
    this.logger.warn(`Blocking threat source: ${ipAddress}`);

    // This would typically integrate with your firewall or security system
    // For now, we'll just log the action
    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_VIOLATION,
      `Blocked threat source: ${ipAddress}`,
      { ipAddress, action: 'blocked' },
      AuditSeverity.HIGH,
    );
  }

  private async quarantineFile(fileName?: string): Promise<void> {
    if (!fileName) return;

    this.logger.warn(`Quarantining suspicious file: ${fileName}`);

    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_VIOLATION,
      `Quarantined suspicious file: ${fileName}`,
      { fileName, action: 'quarantined' },
      AuditSeverity.HIGH,
    );
  }

  async getThreatIntelligence(
    ipAddress: string,
  ): Promise<ThreatIntelligence | null> {
    return this.threatIntelligenceCache.get(ipAddress) || null;
  }

  async getAllThreatIntelligence(): Promise<ThreatIntelligence[]> {
    return Array.from(this.threatIntelligenceCache.values());
  }

  @Cron('0 0 * * *') // Daily at midnight
  async performDailySecurityAudit(): Promise<void> {
    this.logger.log('Performing daily security audit');

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      const report = await this.generateSecurityAuditReport(yesterday, today);

      // Store the report or send to security team
      await this.auditLogService.logSecurity(
        AuditEventType.SECURITY_SCAN,
        'Daily security audit completed',
        {
          reportSummary: report.summary,
          activeThreats: report.summary.activeThreats,
          criticalEvents: report.summary.criticalEvents,
        },
        AuditSeverity.LOW,
      );

      // Alert on critical findings
      if (
        report.summary.criticalEvents > 0 ||
        report.summary.activeThreats > 0
      ) {
        await this.auditLogService.logSecurity(
          AuditEventType.SECURITY_VIOLATION,
          'Daily security audit found critical issues',
          {
            criticalEvents: report.summary.criticalEvents,
            activeThreats: report.summary.activeThreats,
            recommendations: report.recommendations,
          },
          AuditSeverity.CRITICAL,
        );
      }
    } catch (error) {
      this.logger.error('Daily security audit failed:', error);
      await this.auditLogService.logSecurity(
        AuditEventType.SYSTEM_EVENT,
        'Daily security audit failed',
        { error: error.message },
        AuditSeverity.HIGH,
      );
    }
  }

  @Cron('0 */6 * * *') // Every 6 hours
  async cleanupThreatIntelligenceCache(): Promise<void> {
    this.logger.log('Cleaning up threat intelligence cache');

    const cutoffTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago

    const entries = Array.from(this.threatIntelligenceCache.entries());
    for (const [ipAddress, intelligence] of entries) {
      if (intelligence.lastSeen < cutoffTime) {
        this.threatIntelligenceCache.delete(ipAddress);
      }
    }

    this.logger.log(
      `Threat intelligence cache cleaned up. Current size: ${this.threatIntelligenceCache.size}`,
    );
  }

  // Helper method to map AuditLog to SecurityEvent
  private mapAuditLogToSecurityEvent(log: AuditLog): SecurityEvent {
    const metadata = (log.metadata as any) || {};

    return {
      id: log.id,
      eventType:
        metadata.securityEventType || SecurityEventType.POLICY_VIOLATION,
      threatLevel: metadata.threatLevel || SecurityThreatLevel.MEDIUM,
      status: metadata.securityStatus || SecurityEventStatus.ACTIVE,
      timestamp: log.createdAt,
      ipAddress: log.ipAddress || undefined,
      userAgent: log.userAgent || undefined,
      userId: log.userId || undefined,
      details: metadata.details || {},
      isBlocked: metadata.isBlocked || false,
      resolvedAt: metadata.resolvedAt
        ? new Date(metadata.resolvedAt)
        : undefined,
      mitigationActions: metadata.mitigationActions || undefined,
      requiresInvestigation: metadata.requiresInvestigation || false,

      // Helper methods
      isResolved: () => {
        const status = metadata.securityStatus || SecurityEventStatus.ACTIVE;
        return (
          status === SecurityEventStatus.RESOLVED ||
          status === SecurityEventStatus.FALSE_POSITIVE
        );
      },
      isActive: () => {
        const status = metadata.securityStatus || SecurityEventStatus.ACTIVE;
        return status === SecurityEventStatus.ACTIVE;
      },
      isRecurring: () => metadata.isRecurring || false,
      shouldAutoRemediate: () => {
        const threatLevel = metadata.threatLevel || SecurityThreatLevel.MEDIUM;
        const eventType = metadata.securityEventType;
        return (
          [
            SecurityEventType.BRUTE_FORCE_ATTACK,
            SecurityEventType.RATE_LIMIT_EXCEEDED,
            SecurityEventType.SUSPICIOUS_FILE_UPLOAD,
          ].includes(eventType) && threatLevel !== SecurityThreatLevel.CRITICAL
        );
      },
      escalationRequired: () => {
        const ageInMinutes =
          (new Date().getTime() - log.createdAt.getTime()) / (1000 * 60);
        const threatLevel = metadata.threatLevel || SecurityThreatLevel.MEDIUM;

        if (threatLevel === SecurityThreatLevel.CRITICAL)
          return ageInMinutes > 15;
        if (threatLevel === SecurityThreatLevel.HIGH) return ageInMinutes > 60;
        if (threatLevel === SecurityThreatLevel.MEDIUM)
          return ageInMinutes > 240; // 4 hours
        return ageInMinutes > 1440; // 24 hours
      },
      getAgeInMinutes: () => {
        return (new Date().getTime() - log.createdAt.getTime()) / (1000 * 60);
      },
    };
  }

  // Helper method to update security event status
  private async updateSecurityEventStatus(
    eventId: string,
    updates: {
      status?: SecurityEventStatus;
      mitigationActions?: string;
      requiresInvestigation?: boolean;
      responseDetails?: any;
    },
  ): Promise<void> {
    const log = await this.prisma.auditLog.findUnique({
      where: { id: eventId },
    });
    if (!log) {
      throw new Error(`Security event ${eventId} not found`);
    }

    const metadata = (log.metadata as any) || {};

    if (updates.status) metadata.securityStatus = updates.status;
    if (updates.mitigationActions)
      metadata.mitigationActions = updates.mitigationActions;
    if (updates.requiresInvestigation !== undefined)
      metadata.requiresInvestigation = updates.requiresInvestigation;
    if (updates.responseDetails)
      metadata.responseDetails = updates.responseDetails;
    if (
      updates.status === SecurityEventStatus.RESOLVED ||
      updates.status === SecurityEventStatus.FALSE_POSITIVE
    ) {
      metadata.resolvedAt = new Date().toISOString();
    }

    await this.prisma.auditLog.update({
      where: { id: eventId },
      data: {
        metadata,
      },
    });
  }

  // Method to create a security event
  async createSecurityEvent(
    eventType: SecurityEventType,
    threatLevel: SecurityThreatLevel,
    description: string,
    details: Record<string, any> = {},
    ipAddress?: string,
    userId?: string,
  ): Promise<SecurityEvent> {
    // Create audit log entry with security metadata
    const auditLog = await this.auditLogService.log({
      eventType: AuditEventType.SECURITY_VIOLATION,
      severity: this.mapThreatLevelToSeverity(threatLevel),
      resource: 'security',
      action: 'security_event',
      description,
      details,
      userId,
      ipAddress,
      source: 'security-audit-service',
      isSecurityRelated: true,
      requiresReview:
        threatLevel === SecurityThreatLevel.HIGH ||
        threatLevel === SecurityThreatLevel.CRITICAL,
      metadata: {
        securityEventType: eventType,
        threatLevel,
        securityStatus: SecurityEventStatus.ACTIVE,
        isBlocked: false,
      },
    });

    return this.mapAuditLogToSecurityEvent(auditLog as AuditLog);
  }

  private mapThreatLevelToSeverity(
    threatLevel: SecurityThreatLevel,
  ): AuditSeverity {
    switch (threatLevel) {
      case SecurityThreatLevel.LOW:
        return AuditSeverity.LOW;
      case SecurityThreatLevel.MEDIUM:
        return AuditSeverity.MEDIUM;
      case SecurityThreatLevel.HIGH:
        return AuditSeverity.HIGH;
      case SecurityThreatLevel.CRITICAL:
        return AuditSeverity.CRITICAL;
      default:
        return AuditSeverity.MEDIUM;
    }
  }
}
