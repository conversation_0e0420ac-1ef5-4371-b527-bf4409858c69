#!/bin/bash

# Start all Luminar applications in parallel using pnpm workspace features
# This script leverages the monorepo structure for efficient parallel execution

echo "🚀 Starting Luminar L&D Platform - All Applications"
echo ""

# Check if infrastructure is already running
if docker-compose -f docker-compose.yml ps | grep -q "Up"; then
    echo "✅ Infrastructure services are already running"
else
    echo "📦 Starting infrastructure services..."
    docker-compose -f docker-compose.yml up -d postgres redis elasticsearch rabbitmq minio qdrant ollama
    echo "⏳ Waiting for services to be healthy..."
    sleep 15  # Increased wait time for additional services
    
    # Check if AI/ML services are ready
    echo "🤖 Checking AI/ML services..."
    until curl -s http://localhost:6333/readiness > /dev/null 2>&1; do
        echo "  Waiting for Qdrant to be ready..."
        sleep 2
    done
    echo "  ✅ Qdrant is ready"
    
    until curl -s http://localhost:9434/api/tags > /dev/null 2>&1; do
        echo "  Waiting for Ollama to be ready..."
        sleep 2
    done
    echo "  ✅ Ollama is ready"
fi

echo ""
echo "🏗️ Starting all applications in parallel..."
echo ""

# Start all apps in parallel using pnpm workspace commands
echo "Starting frontend applications..."
pnpm --filter './apps/*' --parallel run dev &

# Start Command Center separately as it uses start:dev
echo "Starting Command Center API..."
pnpm --filter './apps/command-center' run start:dev &

# Start Python services
echo "Starting Python services..."
(cd apps/python-services && source venv/bin/activate && python main.py) &

echo ""
echo "✅ All applications starting..."
echo ""
echo "Application URLs (Updated Ports):"
echo "  AMNA (AI Assistant): http://localhost:5001"
echo "  E-Connect (Email): http://localhost:5002"
echo "  Lighthouse (Knowledge Base): http://localhost:5003"
echo "  Luminar Dashboard: http://localhost:5004"
echo "  Training Analysis: http://localhost:5005"
echo "  Vendors Management: http://localhost:5006"
echo "  Wins Tracking: http://localhost:5007"
echo "  Shell: http://localhost:5008"
echo "  Command Center API: http://localhost:3000"
echo "  Python Services: http://localhost:8001"
echo ""
echo "Infrastructure URLs:"
echo "  PostgreSQL: localhost:6432"
echo "  Redis: localhost:6379"
echo "  Elasticsearch: localhost:6200"
echo "  RabbitMQ Management: localhost:6673"
echo "  MinIO Console: localhost:9001"
echo ""
echo "AI/ML Services:"
echo "  Qdrant (Vector DB): localhost:6333"
echo "  Qdrant Dashboard: http://localhost:6333/dashboard"
echo "  Ollama (Local LLM): localhost:9434"
echo "  Ollama API: http://localhost:9434/api/tags"
echo ""
echo "💡 Press Ctrl+C to stop all services"
echo ""

# Wait for all background processes
wait